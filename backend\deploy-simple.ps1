# WOSM Go Backend Simple Deploy Script

param(
    [string]$Action = "status"
)

$ServiceName = "wosm"
$BinaryName = "wosm.exe"
$Port = 8080
$HealthCheckUrl = "http://localhost:$Port/health"

function Write-Info($message) {
    Write-Host "ℹ️  $message" -ForegroundColor Cyan
}

function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Get-ServiceStatus {
    try {
        $response = Invoke-RestMethod -Uri $HealthCheckUrl -Method Get -TimeoutSec 3
        if ($response.status -eq "up") {
            return @{
                Running = $true
                Status = "Healthy"
                Version = $response.version
                Service = $response.service
            }
        }
    } catch {
        $process = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
        if ($process) {
            return @{
                Running = $true
                Status = "Unhealthy"
                PID = $process.Id
            }
        }
    }
    
    return @{
        Running = $false
        Status = "Stopped"
    }
}

function Start-WosmService {
    Write-Info "启动 WOSM 服务..."
    
    $status = Get-ServiceStatus
    if ($status.Running) {
        Write-Warning "服务已在运行中"
        return
    }
    
    if (-not (Test-Path $BinaryName)) {
        Write-Error "未找到可执行文件: $BinaryName"
        return
    }
    
    $process = Start-Process -FilePath ".\$BinaryName" -PassThru -WindowStyle Hidden
    Write-Info "等待服务启动..."
    
    for ($i = 0; $i -lt 30; $i++) {
        Start-Sleep -Seconds 1
        $status = Get-ServiceStatus
        if ($status.Running -and $status.Status -eq "Healthy") {
            Write-Success "服务启动成功!"
            Write-Info "PID: $($process.Id)"
            Write-Info "版本: $($status.Version)"
            return
        }
    }
    
    Write-Error "服务启动超时"
}

function Stop-WosmService {
    Write-Info "停止 WOSM 服务..."
    
    $processes = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
    if (-not $processes) {
        Write-Warning "服务未运行"
        return
    }
    
    foreach ($process in $processes) {
        Write-Info "停止进程 PID: $($process.Id)"
        $process.Kill()
    }
    
    Write-Success "服务已停止"
}

function Show-WosmStatus {
    Write-Host "=== WOSM 服务状态 ===" -ForegroundColor Blue
    
    $status = Get-ServiceStatus
    
    if ($status.Running) {
        if ($status.Status -eq "Healthy") {
            Write-Success "服务运行正常"
            Write-Info "服务: $($status.Service)"
            Write-Info "版本: $($status.Version)"
            Write-Info "端口: $Port"
        } else {
            Write-Warning "服务运行异常"
            if ($status.PID) {
                Write-Info "PID: $($status.PID)"
            }
        }
    } else {
        Write-Warning "服务未运行"
    }
    
    # 显示端口占用
    $portInfo = netstat -ano | findstr ":$Port "
    if ($portInfo) {
        Write-Info "端口占用情况:"
        $portInfo | ForEach-Object { Write-Host "  $_" }
    }
}

function Deploy-WosmService {
    Write-Host "=== 部署 WOSM 服务 ===" -ForegroundColor Blue
    
    # 停止现有服务
    Stop-WosmService
    Start-Sleep -Seconds 2
    
    # 构建应用
    Write-Info "构建应用..."
    try {
        go build -o $BinaryName main.go
        Write-Success "构建完成"
    } catch {
        Write-Error "构建失败"
        return
    }
    
    # 启动服务
    Start-WosmService
    
    # 健康检查
    Write-Info "运行健康检查..."
    Start-Sleep -Seconds 3
    
    try {
        $health = Invoke-RestMethod -Uri $HealthCheckUrl -Method Get -TimeoutSec 10
        Write-Success "健康检查通过"
        Write-Info "服务: $($health.service)"
        Write-Info "版本: $($health.version)"
    } catch {
        Write-Error "健康检查失败"
        return
    }
    
    Write-Success "部署完成!"
}

# 主逻辑
switch ($Action.ToLower()) {
    "deploy" {
        Deploy-WosmService
    }
    "start" {
        Start-WosmService
    }
    "stop" {
        Stop-WosmService
    }
    "restart" {
        Stop-WosmService
        Start-Sleep -Seconds 2
        Start-WosmService
    }
    "status" {
        Show-WosmStatus
    }
    default {
        Write-Host "=== WOSM 部署脚本 ===" -ForegroundColor Blue
        Write-Host "用法: .\deploy-simple.ps1 [Action]"
        Write-Host ""
        Write-Host "Actions:"
        Write-Host "  deploy   - 部署服务"
        Write-Host "  start    - 启动服务"
        Write-Host "  stop     - 停止服务"
        Write-Host "  restart  - 重启服务"
        Write-Host "  status   - 显示状态"
        Write-Host ""
        Write-Host "示例:"
        Write-Host "  .\deploy-simple.ps1 deploy"
        Write-Host "  .\deploy-simple.ps1 status"
    }
}
