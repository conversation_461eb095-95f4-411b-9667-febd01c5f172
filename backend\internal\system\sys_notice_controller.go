package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysNoticeController 通知公告信息控制器
type SysNoticeController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysNoticeController 创建通知公告控制器
func NewSysNoticeController(logger *zap.Logger, db *gorm.DB) *SysNoticeController {
	return &SysNoticeController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysNoticeController的路由
func (c *SysNoticeController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)            // 通知公告列表
	r.GET("/:noticeId", c.GetInfo)    // 获取通知公告详情
	r.POST("", c.Add)                 // 新增通知公告
	r.PUT("", c.Edit)                 // 修改通知公告
	r.DELETE("/:noticeIds", c.Remove) // 删除通知公告
}

// List 获取通知公告列表 - 完全按照Java后端业务逻辑
func (c *SysNoticeController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var notices []domain.SysNotice
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysNotice{})

	// 公告标题查询
	if noticeTitle := ctx.Query("noticeTitle"); noticeTitle != "" {
		query = query.Where("notice_title LIKE ?", "%"+noticeTitle+"%")
	}

	// 公告类型查询
	if noticeType := ctx.Query("noticeType"); noticeType != "" {
		query = query.Where("notice_type = ?", noticeType)
	}

	// 公告状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 创建者查询
	if createBy := ctx.Query("createBy"); createBy != "" {
		query = query.Where("create_by LIKE ?", "%"+createBy+"%")
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("notice_id DESC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&notices).Error; err != nil {
		c.Logger.Error("查询通知公告列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  notices,
	})
}

// GetInfo 根据公告ID获取详细信息 - 完全按照Java后端逻辑
func (c *SysNoticeController) GetInfo(ctx *gin.Context) {
	noticeIdStr := ctx.Param("noticeId")
	noticeId, err := strconv.ParseInt(noticeIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "公告ID格式错误")
		return
	}

	var notice domain.SysNotice
	if err := c.db.Where("notice_id = ?", noticeId).First(&notice).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "通知公告不存在")
		} else {
			c.Logger.Error("查询通知公告信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, notice)
}

// Add 新增通知公告 - 完全按照Java后端业务逻辑
func (c *SysNoticeController) Add(ctx *gin.Context) {
	var notice domain.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if notice.NoticeTitle == "" {
		c.ErrorWithMessage(ctx, "公告标题不能为空")
		return
	}
	if notice.NoticeType == "" {
		c.ErrorWithMessage(ctx, "公告类型不能为空")
		return
	}
	if notice.NoticeContent == "" {
		c.ErrorWithMessage(ctx, "公告内容不能为空")
		return
	}
	if len(notice.NoticeTitle) > 50 {
		c.ErrorWithMessage(ctx, "公告标题不能超过50个字符")
		return
	}
	if len(notice.NoticeContent) > 2000 {
		c.ErrorWithMessage(ctx, "公告内容不能超过2000个字符")
		return
	}

	// 验证公告类型
	if notice.NoticeType != "1" && notice.NoticeType != "2" {
		c.ErrorWithMessage(ctx, "公告类型只能是1(通知)或2(公告)")
		return
	}

	// 设置默认值
	notice.CreateBy = c.GetUsername(ctx)
	if notice.Status == "" {
		notice.Status = "0" // 默认正常状态
	}

	// 保存通知公告
	if err := c.db.Create(&notice).Error; err != nil {
		c.Logger.Error("新增通知公告失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改通知公告 - 完全按照Java后端业务逻辑
func (c *SysNoticeController) Edit(ctx *gin.Context) {
	var notice domain.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if notice.NoticeId == 0 {
		c.ErrorWithMessage(ctx, "公告ID不能为空")
		return
	}
	if notice.NoticeTitle == "" {
		c.ErrorWithMessage(ctx, "公告标题不能为空")
		return
	}
	if notice.NoticeType == "" {
		c.ErrorWithMessage(ctx, "公告类型不能为空")
		return
	}
	if notice.NoticeContent == "" {
		c.ErrorWithMessage(ctx, "公告内容不能为空")
		return
	}
	if len(notice.NoticeTitle) > 50 {
		c.ErrorWithMessage(ctx, "公告标题不能超过50个字符")
		return
	}
	if len(notice.NoticeContent) > 2000 {
		c.ErrorWithMessage(ctx, "公告内容不能超过2000个字符")
		return
	}

	// 验证公告类型
	if notice.NoticeType != "1" && notice.NoticeType != "2" {
		c.ErrorWithMessage(ctx, "公告类型只能是1(通知)或2(公告)")
		return
	}

	// 检查通知公告是否存在
	var existingNotice domain.SysNotice
	if err := c.db.Where("notice_id = ?", notice.NoticeId).First(&existingNotice).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "通知公告不存在")
		} else {
			c.Logger.Error("查询通知公告失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 设置更新者
	notice.UpdateBy = c.GetUsername(ctx)

	// 更新通知公告信息
	updateData := map[string]interface{}{
		"notice_title":   notice.NoticeTitle,
		"notice_type":    notice.NoticeType,
		"notice_content": notice.NoticeContent,
		"status":         notice.Status,
		"update_by":      notice.UpdateBy,
		"remark":         notice.Remark,
	}

	if err := c.db.Model(&existingNotice).Updates(updateData).Error; err != nil {
		c.Logger.Error("修改通知公告失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除通知公告 - 完全按照Java后端业务逻辑
func (c *SysNoticeController) Remove(ctx *gin.Context) {
	noticeIdsStr := ctx.Param("noticeIds")
	noticeIdStrs := strings.Split(noticeIdsStr, ",")

	var noticeIds []int64
	for _, noticeIdStr := range noticeIdStrs {
		noticeId, err := strconv.ParseInt(noticeIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "公告ID格式错误")
			return
		}
		noticeIds = append(noticeIds, noticeId)
	}

	// 删除通知公告
	if err := c.db.Where("notice_id IN ?", noticeIds).Delete(&domain.SysNotice{}).Error; err != nil {
		c.Logger.Error("删除通知公告失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysNoticeController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysNoticeController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysNoticeController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysNoticeController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
