# Debug Department Creation Issue

Write-Host "=== Debug Department Creation Issue ===" -ForegroundColor Blue

# Get authentication token
$loginBody = '{"username":"admin","password":"admin123"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
$token = $loginResponse.data.token
$headers = @{ Authorization = "Bearer $token" }

Write-Host "Token obtained successfully" -ForegroundColor Green

# Test with different parent IDs
$testCases = @(
    @{parentId = 0; name = "Test Root Dept"},
    @{parentId = 101; name = "Test Sub Dept"},
    @{parentId = 102; name = "Test Sub Dept 2"}
)

foreach ($testCase in $testCases) {
    Write-Host ""
    Write-Host "Testing with parentId: $($testCase.parentId), name: $($testCase.name)" -ForegroundColor Yellow
    
    try {
        $deptBody = @{
            deptName = $testCase.name
            parentId = $testCase.parentId
            orderNum = 1
            leader = "Test Leader"
            phone = "13800138000"
            email = "<EMAIL>"
            status = "0"
        } | ConvertTo-Json
        
        $deptResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept" -Method Post -Body $deptBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
        
        if ($deptResponse.code -eq 200) {
            Write-Host "✅ Success: Department created with parentId $($testCase.parentId)" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed: $($deptResponse.msg)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error: $_" -ForegroundColor Red
    }
}

# Test with minimal data
Write-Host ""
Write-Host "Testing with minimal data..." -ForegroundColor Yellow
try {
    $minimalDeptBody = @{
        deptName = "Minimal Test Dept"
        parentId = 0
        orderNum = 1
    } | ConvertTo-Json
    
    $minimalResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept" -Method Post -Body $minimalDeptBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    
    if ($minimalResponse.code -eq 200) {
        Write-Host "✅ Success: Minimal department created" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed: $($minimalResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug completed!" -ForegroundColor Blue
