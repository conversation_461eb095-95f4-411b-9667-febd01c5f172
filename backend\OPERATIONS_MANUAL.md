# WOSM Go后端运维手册

## 🚀 快速启动指南

### 1. 环境检查
```bash
# 检查Go版本
go version

# 检查数据库连接
sqlcmd -S localhost -U sa -P "F@2233" -Q "SELECT @@VERSION"

# 检查Redis连接（可选）
redis-cli ping
```

### 2. 启动服务
```bash
# 进入项目目录
cd D:\wosm\backend

# 编译项目
go build -o wosm.exe main.go

# 启动服务
.\wosm.exe
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:8080/health

# 登录测试
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 📊 监控和维护

### 系统监控指标

#### 1. 应用监控
- **CPU使用率**: 正常 < 50%，警告 > 80%
- **内存使用**: 正常 < 1GB，警告 > 2GB
- **响应时间**: 正常 < 200ms，警告 > 1s
- **错误率**: 正常 < 1%，警告 > 5%

#### 2. 数据库监控
- **连接数**: 正常 < 50，警告 > 100
- **查询时间**: 正常 < 100ms，警告 > 500ms
- **死锁**: 应为0
- **磁盘空间**: 警告 < 20%剩余

#### 3. 缓存监控
- **Redis连接**: 正常连接，断开时自动降级
- **缓存命中率**: 正常 > 80%
- **内存使用**: 正常 < 1GB

### 日志管理

#### 日志位置
- **应用日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **访问日志**: `logs/access.log`

#### 日志级别
- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARN**: 警告信息
- **ERROR**: 错误信息
- **FATAL**: 致命错误

#### 日志轮转
```bash
# 手动轮转日志
mv logs/app.log logs/app.log.$(date +%Y%m%d)
touch logs/app.log
```

## 🔧 常见问题排查

### 1. 服务启动失败

#### 问题：端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :8080

# 杀死占用进程
taskkill /PID <进程ID> /F
```

#### 问题：数据库连接失败
```bash
# 检查数据库服务
net start MSSQLSERVER

# 测试连接
sqlcmd -S localhost -U sa -P "F@2233" -Q "SELECT 1"
```

#### 问题：配置文件错误
```bash
# 验证配置文件语法
go run -tags=jsoniter main.go --config-check
```

### 2. 性能问题

#### 响应时间过长
1. 检查数据库查询性能
2. 检查缓存命中率
3. 分析慢查询日志
4. 优化数据库索引

#### 内存使用过高
1. 检查内存泄漏
2. 调整GC参数
3. 优化缓存策略
4. 重启服务释放内存

### 3. 安全问题

#### 异常登录
1. 检查登录日志
2. 分析IP来源
3. 启用IP白名单
4. 加强密码策略

#### 攻击检测
1. 检查安全日志
2. 分析攻击模式
3. 更新安全规则
4. 封禁恶意IP

## 🔄 备份和恢复

### 数据备份

#### 1. 数据库备份
```sql
-- 完整备份
BACKUP DATABASE wosm TO DISK = 'D:\backup\wosm_full.bak'

-- 差异备份
BACKUP DATABASE wosm TO DISK = 'D:\backup\wosm_diff.bak' 
WITH DIFFERENTIAL

-- 日志备份
BACKUP LOG wosm TO DISK = 'D:\backup\wosm_log.trn'
```

#### 2. 配置备份
```bash
# 备份配置文件
copy configs\config.yaml backup\config_$(date +%Y%m%d).yaml

# 备份日志文件
xcopy logs backup\logs_$(date +%Y%m%d) /E /I
```

### 数据恢复

#### 1. 数据库恢复
```sql
-- 完整恢复
RESTORE DATABASE wosm FROM DISK = 'D:\backup\wosm_full.bak'
WITH REPLACE

-- 差异恢复
RESTORE DATABASE wosm FROM DISK = 'D:\backup\wosm_full.bak' WITH NORECOVERY
RESTORE DATABASE wosm FROM DISK = 'D:\backup\wosm_diff.bak' WITH RECOVERY
```

#### 2. 应用恢复
```bash
# 停止服务
taskkill /F /IM wosm.exe

# 恢复配置
copy backup\config_20250613.yaml configs\config.yaml

# 重启服务
.\wosm.exe
```

## 📈 性能优化

### 1. 数据库优化

#### 索引优化
```sql
-- 检查缺失索引
SELECT * FROM sys.dm_db_missing_index_details

-- 创建索引
CREATE INDEX IX_sys_user_username ON sys_user(user_name)
CREATE INDEX IX_sys_user_status ON sys_user(status)
```

#### 查询优化
```sql
-- 检查慢查询
SELECT TOP 10 
    total_elapsed_time/execution_count AS avg_time,
    text
FROM sys.dm_exec_query_stats 
CROSS APPLY sys.dm_exec_sql_text(sql_handle)
ORDER BY avg_time DESC
```

### 2. 应用优化

#### 缓存优化
```go
// 增加缓存时间
cache.Set("user:1", user, 30*time.Minute)

// 使用缓存预热
go func() {
    preloadCache()
}()
```

#### 连接池优化
```go
// 调整数据库连接池
db.SetMaxOpenConns(100)
db.SetMaxIdleConns(10)
db.SetConnMaxLifetime(time.Hour)
```

## 🔐 安全维护

### 1. 定期安全检查

#### 密码策略
- 定期更新数据库密码
- 强制用户密码复杂度
- 启用密码过期策略

#### 证书管理
- 检查SSL证书有效期
- 定期更新证书
- 配置证书自动续期

### 2. 安全更新

#### 依赖更新
```bash
# 检查依赖更新
go list -u -m all

# 更新依赖
go get -u ./...
go mod tidy
```

#### 安全补丁
- 定期检查Go版本更新
- 关注安全公告
- 及时应用安全补丁

## 📋 维护计划

### 日常维护 (每日)
- [ ] 检查服务运行状态
- [ ] 查看错误日志
- [ ] 监控系统资源使用
- [ ] 检查备份任务执行

### 周维护 (每周)
- [ ] 分析性能报告
- [ ] 清理过期日志
- [ ] 检查磁盘空间
- [ ] 更新监控报告

### 月维护 (每月)
- [ ] 数据库性能优化
- [ ] 安全漏洞扫描
- [ ] 备份恢复测试
- [ ] 容量规划评估

### 季度维护 (每季度)
- [ ] 系统架构评估
- [ ] 性能基准测试
- [ ] 灾难恢复演练
- [ ] 安全审计报告

## 📞 应急响应

### 1. 服务中断

#### 立即响应
1. 检查服务状态
2. 查看错误日志
3. 重启服务
4. 通知相关人员

#### 根因分析
1. 收集日志信息
2. 分析故障原因
3. 制定修复方案
4. 实施修复措施

### 2. 安全事件

#### 立即响应
1. 隔离受影响系统
2. 保存证据信息
3. 评估影响范围
4. 启动应急预案

#### 后续处理
1. 修复安全漏洞
2. 加强安全措施
3. 更新安全策略
4. 总结经验教训

## 📊 监控脚本

### 健康检查脚本
```powershell
# health-check.ps1
$response = Invoke-RestMethod -Uri "http://localhost:8080/health" -TimeoutSec 10
if ($response.status -eq "up") {
    Write-Host "Service is healthy" -ForegroundColor Green
} else {
    Write-Host "Service is unhealthy" -ForegroundColor Red
    # 发送告警
}
```

### 性能监控脚本
```powershell
# performance-monitor.ps1
$process = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
if ($process) {
    $cpu = $process.CPU
    $memory = $process.WorkingSet64 / 1MB
    Write-Host "CPU: $cpu, Memory: ${memory}MB"
    
    if ($memory -gt 1000) {
        Write-Host "High memory usage detected!" -ForegroundColor Yellow
    }
}
```

---

**运维手册版本**: 1.0  
**最后更新**: 2025年6月13日  
**维护负责人**: 系统管理员
