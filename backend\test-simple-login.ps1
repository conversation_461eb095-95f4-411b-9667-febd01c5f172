# Simple Login Test

Write-Host "=== Simple Login Test ===" -ForegroundColor Blue

# Test 1: Health check
Write-Host "1. Testing health endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8080/health" -Method Get -TimeoutSec 10
    Write-Host "Health check successful: $($healthResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Captcha endpoint
Write-Host ""
Write-Host "2. Testing captcha endpoint..." -ForegroundColor Yellow
try {
    $captchaResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/captchaImage" -Method Get -TimeoutSec 10
    Write-Host "Captcha successful: $($captchaResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Captcha failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Simple login without security middleware
Write-Host ""
Write-Host "3. Testing simple login..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    Write-Host "Login body: $loginBody" -ForegroundColor Gray
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/simple/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    Write-Host "Simple login successful: $($loginResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Simple login failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# Test 4: Demo login
Write-Host ""
Write-Host "4. Testing demo login..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $demoLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/demo/public/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    Write-Host "Demo login successful: $($demoLoginResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Demo login failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Main login with verbose error handling
Write-Host ""
Write-Host "5. Testing main login with verbose error handling..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $headers = @{
        "Content-Type" = "application/json"
        "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        "Accept" = "application/json"
    }
    
    $mainLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -Headers $headers -TimeoutSec 10
    Write-Host "Main login successful: $($mainLoginResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Main login failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
        
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "Simple login test completed!" -ForegroundColor Green
