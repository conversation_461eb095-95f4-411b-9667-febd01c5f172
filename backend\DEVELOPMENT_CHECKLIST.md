# WOSM开发检查清单

## 🎯 核心开发原则检查

在开始任何功能开发前，必须确认：
- [ ] 已仔细分析对应的Java控制器代码
- [ ] 理解所有业务逻辑和验证规则
- [ ] 明确所有API接口的输入输出格式
- [ ] 了解权限控制和数据权限要求

## 📋 每个控制器开发检查清单

### 1. 路由注册检查
- [ ] 所有路由路径与Java完全一致
- [ ] HTTP方法（GET/POST/PUT/DELETE）与Java一致
- [ ] 路由参数格式与Java一致
- [ ] 中间件使用与Java权限控制一致

### 2. 方法实现检查
- [ ] 所有Java方法都有对应的Go实现
- [ ] 方法签名和参数与Java一致
- [ ] 返回值格式与Java一致
- [ ] 没有遗漏任何业务方法

### 3. 参数验证检查
- [ ] 请求参数绑定与Java一致
- [ ] 参数验证规则与Java一致
- [ ] 必填参数检查与Java一致
- [ ] 参数格式验证与Java一致

### 4. 业务逻辑检查
- [ ] 数据查询逻辑与Java一致
- [ ] 数据处理流程与Java一致
- [ ] 业务规则验证与Java一致
- [ ] 数据关联处理与Java一致

### 5. 权限控制检查
- [ ] 用户权限验证与Java一致
- [ ] 数据权限控制与Java一致
- [ ] 角色权限检查与Java一致
- [ ] 操作权限验证与Java一致

### 6. 响应格式检查
- [ ] 成功响应格式与Java一致
- [ ] 错误响应格式与Java一致
- [ ] 分页响应格式与Java一致
- [ ] 数据字段名称与Java一致

### 7. 错误处理检查
- [ ] 异常捕获与Java一致
- [ ] 错误码与Java一致
- [ ] 错误信息与Java一致
- [ ] 错误日志记录与Java一致

### 8. 数据库操作检查
- [ ] SQL查询逻辑与Java一致
- [ ] 事务处理与Java一致
- [ ] 数据更新逻辑与Java一致
- [ ] 软删除处理与Java一致

## 🔍 具体功能模块检查清单

### 角色管理模块 (SysRoleController)
- [ ] `/list` - 角色列表查询
  - [ ] 分页参数处理
  - [ ] 查询条件过滤
  - [ ] 数据权限控制
  - [ ] 响应格式：{total, rows}

- [ ] `/add` - 新增角色
  - [ ] 参数验证（角色名、权限标识）
  - [ ] 唯一性检查
  - [ ] 创建者设置
  - [ ] 默认值设置

- [ ] `/edit` - 修改角色
  - [ ] 角色存在性检查
  - [ ] 权限验证
  - [ ] 唯一性检查（排除自己）
  - [ ] 更新者设置

- [ ] `/remove` - 删除角色
  - [ ] 批量删除支持
  - [ ] 关联数据检查
  - [ ] 软删除处理
  - [ ] 权限验证

- [ ] `/dataScope` - 数据权限设置
  - [ ] 数据范围验证
  - [ ] 部门关联处理
  - [ ] 权限验证

- [ ] `/changeStatus` - 状态修改
  - [ ] 状态值验证
  - [ ] 权限检查
  - [ ] 更新处理

- [ ] `/optionselect` - 角色选择框
  - [ ] 只返回启用角色
  - [ ] 数据权限过滤
  - [ ] 简化字段返回

### 菜单管理模块 (SysMenuController)
- [ ] `/list` - 菜单列表
  - [ ] 树形结构处理
  - [ ] 权限过滤
  - [ ] 层级关系

- [ ] `/treeselect` - 菜单树选择
  - [ ] 树形数据格式
  - [ ] 权限过滤

- [ ] `/roleMenuTreeselect/:roleId` - 角色菜单树
  - [ ] 角色已选菜单
  - [ ] 树形结构
  - [ ] 权限验证

### 部门管理模块 (SysDeptController)
- [ ] `/list` - 部门列表
  - [ ] 树形结构
  - [ ] 数据权限控制
  - [ ] 层级关系

- [ ] `/list/exclude/:deptId` - 排除指定部门
  - [ ] 排除逻辑
  - [ ] 子部门排除

### 用户管理模块 (SysUserController)
- [ ] 所有CRUD操作已完成
- [ ] 权限验证已实现
- [ ] 数据关联已处理

## 🧪 测试验证检查清单

### API测试
- [ ] 所有接口返回正确的HTTP状态码
- [ ] 响应JSON格式与Java一致
- [ ] 错误情况处理正确
- [ ] 分页功能正常

### 业务逻辑测试
- [ ] 数据验证规则正确
- [ ] 业务流程完整
- [ ] 权限控制有效
- [ ] 数据关联正确

### 前端集成测试
- [ ] 前端能正常调用所有API
- [ ] 数据显示格式正确
- [ ] 操作功能正常
- [ ] 权限控制生效

### 权限测试
- [ ] 不同角色访问权限正确
- [ ] 数据权限控制有效
- [ ] 操作权限验证正确
- [ ] 未授权访问被拒绝

## 🚨 质量门禁

### 代码提交前检查
- [ ] 所有检查清单项目已完成
- [ ] 代码编译无错误
- [ ] 单元测试通过
- [ ] 集成测试通过

### 功能完成标准
- [ ] 与Java后端行为完全一致
- [ ] 前端集成测试通过
- [ ] 权限控制测试通过
- [ ] 性能测试满足要求

## 📝 开发记录

### 当前开发状态
- 开发人员：AI Assistant
- 开发方案：方案二（基于现有Go后端继续开发）
- 开发原则：完全按照Java后端业务逻辑实现

### 完成情况记录
```
[日期] [模块] [状态] [备注]
示例：
2024-01-15 用户管理 ✅完成 所有功能与Java一致
2024-01-16 角色管理 🔄进行中 正在实现权限分配
```

## 🔄 持续改进

### 发现问题时的处理流程
1. 立即停止当前开发
2. 重新分析Java后端对应功能
3. 修正Go后端实现
4. 重新测试验证
5. 更新检查清单

### 质量提升措施
- 定期回顾开发质量
- 持续优化开发流程
- 加强测试覆盖率
- 完善文档记录

---

**重要提醒：这个检查清单是确保开发质量的重要工具，每个功能开发都必须严格按照清单执行！**
