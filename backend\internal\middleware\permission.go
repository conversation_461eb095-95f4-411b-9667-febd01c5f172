package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/model"
)

// PermissionMiddleware 权限中间件
func PermissionMiddleware(logger *zap.Logger, permissionChecker *auth.PermissionChecker) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求路径和方法
		path := c.Request.URL.Path
		method := c.Request.Method

		// TODO: 根据路径和方法获取需要的权限
		// 这里需要从配置或数据库中获取路径对应的权限
		// 暂时使用简单的映射关系
		requiredPermission := getRequiredPermission(path, method)

		// 如果不需要权限，则直接通过
		if requiredPermission == "" {
			c.Next()
			return
		}

		// 检查用户是否有权限
		if !permissionChecker.HasPermi(c, requiredPermission) {
			result := model.AjaxResult{
				Code: http.StatusForbidden,
				Msg:  "没有访问权限，请联系管理员授权",
			}
			c.AbortWithStatusJSON(http.StatusForbidden, result)
			return
		}

		// 继续处理请求
		c.Next()
	}
}

// HasPermission 检查是否有指定权限
func HasPermission(c *gin.Context, permission string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasPermi(c, permission)
}

// HasAnyPermission 检查是否有任意一个权限
func HasAnyPermission(c *gin.Context, permissions []string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasAnyPermi(c, permissions...)
}

// HasRole 检查是否有指定角色
func HasRole(c *gin.Context, role string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasRole(c, role)
}

// HasAnyRole 检查是否有任意一个角色
func HasAnyRole(c *gin.Context, roles []string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasAnyRole(c, roles...)
}

// DataScopeMiddleware 数据权限中间件 - 完全按照Java后端DataScopeAspect逻辑
func DataScopeMiddleware(logger *zap.Logger, db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户
		loginUser := GetLoginUserFromPermission(c)
		if loginUser == nil {
			c.Next()
			return
		}

		// 如果是超级管理员，则不过滤数据
		if loginUser.GetUserId() == 1 {
			c.Next()
			return
		}

		// 设置数据权限SQL条件
		dataScopeSQL := buildDataScopeSQL(loginUser, "d", "u")
		if dataScopeSQL != "" {
			c.Set("dataScope", dataScopeSQL)
		}

		c.Next()
	}
}

// buildDataScopeSQL 构建数据权限SQL - 完全按照Java后端逻辑
func buildDataScopeSQL(loginUser *model.LoginUser, deptAlias, userAlias string) string {
	if loginUser == nil {
		return ""
	}

	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return ""
	}

	var conditions []string
	var sqlParts []string

	for _, role := range user.Roles {
		dataScope := role.DataScope

		// 跳过已处理的数据范围或停用的角色
		if contains(conditions, dataScope) || role.Status == "1" {
			continue
		}

		switch dataScope {
		case "1": // 全部数据权限
			return "" // 不需要任何限制
		case "2": // 自定义数据权限
			sqlParts = append(sqlParts, fmt.Sprintf("%s.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = %d)", deptAlias, role.RoleId))
		case "3": // 本部门数据权限
			sqlParts = append(sqlParts, fmt.Sprintf("%s.dept_id = %d", deptAlias, user.DeptId))
		case "4": // 本部门及以下数据权限
			sqlParts = append(sqlParts, fmt.Sprintf("%s.dept_id IN (SELECT dept_id FROM sys_dept WHERE dept_id = %d OR FIND_IN_SET(%d, ancestors))", deptAlias, user.DeptId, user.DeptId))
		case "5": // 仅本人数据权限
			if userAlias != "" {
				sqlParts = append(sqlParts, fmt.Sprintf("%s.user_id = %d", userAlias, user.UserId))
			} else {
				sqlParts = append(sqlParts, fmt.Sprintf("%s.dept_id = 0", deptAlias)) // 不查询任何数据
			}
		}

		conditions = append(conditions, dataScope)
	}

	// 如果没有任何条件，限制不查询任何数据
	if len(sqlParts) == 0 {
		return fmt.Sprintf("%s.dept_id = 0", deptAlias)
	}

	return "(" + strings.Join(sqlParts, " OR ") + ")"
}

// PermissionRequired 权限验证装饰器 - 完全按照Java后端@PreAuthorize逻辑
func PermissionRequired(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if permission == "" {
			c.Next()
			return
		}

		// 获取当前用户
		loginUser := GetLoginUserFromPermission(c)
		if loginUser == nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "请求未授权，无法访问系统资源",
			})
			return
		}

		// 检查权限
		if !hasPermission(loginUser, permission) {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "没有访问权限，请联系管理员授权",
			})
			return
		}

		c.Next()
	}
}

// RoleRequired 角色验证装饰器 - 完全按照Java后端@PreAuthorize逻辑
func RoleRequired(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(roles) == 0 {
			c.Next()
			return
		}

		// 获取当前用户
		loginUser := GetLoginUserFromPermission(c)
		if loginUser == nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "请求未授权，无法访问系统资源",
			})
			return
		}

		// 检查角色
		if !hasAnyRole(loginUser, roles) {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "没有访问权限，请联系管理员授权",
			})
			return
		}

		c.Next()
	}
}

// hasPermission 检查用户是否有指定权限 - 完全按照Java后端PermissionService逻辑
func hasPermission(loginUser *model.LoginUser, permission string) bool {
	if loginUser == nil || permission == "" {
		return false
	}

	// 超级管理员拥有所有权限
	if loginUser.GetUserId() == 1 {
		return true
	}

	// 检查用户权限
	permissions := loginUser.GetPermissions()
	for _, perm := range permissions {
		if perm == permission || perm == "*:*:*" {
			return true
		}
	}

	return false
}

// hasAnyRole 检查用户是否有任意一个角色 - 完全按照Java后端逻辑
func hasAnyRole(loginUser *model.LoginUser, roles []string) bool {
	if loginUser == nil || len(roles) == 0 {
		return false
	}

	// 超级管理员拥有所有角色
	if loginUser.GetUserId() == 1 {
		return true
	}

	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return false
	}

	// 检查是否有任意一个角色
	for _, reqRole := range roles {
		for _, userRole := range user.Roles {
			if userRole.RoleKey == reqRole || reqRole == "admin" {
				return true
			}
		}
	}

	return false
}

// GetLoginUserFromPermission 从上下文中获取登录用户（权限模块专用）
func GetLoginUserFromPermission(c *gin.Context) *model.LoginUser {
	value, exists := c.Get("loginUser")
	if !exists {
		return nil
	}

	if loginUser, ok := value.(*model.LoginUser); ok {
		return loginUser
	}

	return nil
}

// contains 检查字符串数组是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 根据路径和方法获取需要的权限
func getRequiredPermission(path string, method string) string {
	// 权限映射表 - 完全按照Java后端的权限字符串
	permissionMap := map[string]string{
		"GET:/api/system/user/list": "system:user:list",
		"POST:/api/system/user":     "system:user:add",
		"PUT:/api/system/user":      "system:user:edit",
		"DELETE:/api/system/user":   "system:user:remove",
		"GET:/api/system/role/list": "system:role:list",
		"POST:/api/system/role":     "system:role:add",
		"PUT:/api/system/role":      "system:role:edit",
		"DELETE:/api/system/role":   "system:role:remove",
		"GET:/api/system/menu/list": "system:menu:list",
		"POST:/api/system/menu":     "system:menu:add",
		"PUT:/api/system/menu":      "system:menu:edit",
		"DELETE:/api/system/menu":   "system:menu:remove",
		"GET:/api/system/dept/list": "system:dept:list",
		"POST:/api/system/dept":     "system:dept:add",
		"PUT:/api/system/dept":      "system:dept:edit",
		"DELETE:/api/system/dept":   "system:dept:remove",
	}

	key := method + ":" + path
	return permissionMap[key]
}
