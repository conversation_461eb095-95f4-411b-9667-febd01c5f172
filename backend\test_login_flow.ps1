# 测试完整登录流程
Write-Host "测试完整登录流程..." -ForegroundColor Green

# 1. 获取验证码
Write-Host "1. 获取验证码..."
$captchaResponse = Invoke-RestMethod -Uri 'http://localhost:8080/captchaImage'
Write-Host "验证码响应:" -ForegroundColor Yellow
$captchaResponse | ConvertTo-Json

$uuid = $captchaResponse.uuid
Write-Host "UUID: $uuid" -ForegroundColor Cyan

# 2. 模拟用户输入验证码（这里我们需要从日志中获取正确答案）
Write-Host "`n2. 检查日志获取验证码答案..."
$logContent = Get-Content "logs\ruoyi.log" -Tail 5
$answerLine = $logContent | Where-Object { $_ -match "生成验证码成功.*answer.*(\d+)" }
if ($answerLine) {
    $answer = [regex]::Match($answerLine, '"answer":\s*"([^"]+)"').Groups[1].Value
    Write-Host "从日志中获取的验证码答案: $answer" -ForegroundColor Cyan
} else {
    Write-Host "无法从日志中获取验证码答案，使用默认值" -ForegroundColor Red
    $answer = "1234"
}

# 3. 尝试登录
Write-Host "`n3. 尝试登录..."
$loginBody = @{
    username = "admin"
    password = "admin123"
    code = $answer
    uuid = $uuid
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri 'http://localhost:8080/login' -Method POST -Body $loginBody -ContentType 'application/json'
    Write-Host "登录响应:" -ForegroundColor Yellow
    $loginResponse | ConvertTo-Json -Depth 10
    
    if ($loginResponse.code -eq 200) {
        Write-Host "登录成功！" -ForegroundColor Green
        $token = $loginResponse.token
        Write-Host "Token: $token" -ForegroundColor Cyan
        
        # 4. 测试获取用户信息
        Write-Host "`n4. 测试获取用户信息..."
        $headers = @{
            'Authorization' = "Bearer $token"
        }
        $userInfoResponse = Invoke-RestMethod -Uri 'http://localhost:8080/getInfo' -Headers $headers
        Write-Host "用户信息响应:" -ForegroundColor Yellow
        $userInfoResponse | ConvertTo-Json -Depth 10
        
        Write-Host "`n✅ 完整登录流程测试成功！" -ForegroundColor Green
    } else {
        Write-Host "❌ 登录失败: $($loginResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
