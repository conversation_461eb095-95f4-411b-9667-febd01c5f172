# 前端兼容性测试脚本
Write-Host "🔍 前端兼容性测试开始..." -ForegroundColor Green

$baseUrl = "http://localhost:8080"

# 1. 测试验证码接口
Write-Host "`n1. 测试验证码接口..." -ForegroundColor Yellow
try {
    $captchaResponse = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method GET
    Write-Host "验证码响应格式:" -ForegroundColor Cyan
    Write-Host ($captchaResponse | ConvertTo-Json -Depth 3) -ForegroundColor White
    
    # 检查前端可能期望的字段
    if ($captchaResponse.uuid) {
        Write-Host "✅ uuid字段存在: $($captchaResponse.uuid)" -ForegroundColor Green
    } else {
        Write-Host "❌ uuid字段缺失" -ForegroundColor Red
    }
    
    if ($captchaResponse.img) {
        Write-Host "✅ img字段存在: $($captchaResponse.img.Substring(0, 50))..." -ForegroundColor Green
    } else {
        Write-Host "❌ img字段缺失" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 验证码接口失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试登录接口的多种响应格式
Write-Host "`n2. 测试登录接口响应格式..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123"
    code = ""
    uuid = ""
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "登录响应格式:" -ForegroundColor Cyan
    Write-Host ($loginResponse | ConvertTo-Json -Depth 3) -ForegroundColor White
    
    # 检查前端可能期望的token位置
    Write-Host "`n检查token位置:" -ForegroundColor Yellow
    
    if ($loginResponse.token) {
        Write-Host "✅ 根级别token存在: $($loginResponse.token.Substring(0, 50))..." -ForegroundColor Green
        $token = $loginResponse.token
    } else {
        Write-Host "❌ 根级别token不存在" -ForegroundColor Red
    }
    
    if ($loginResponse.data -and $loginResponse.data.token) {
        Write-Host "✅ data.token存在: $($loginResponse.data.token.Substring(0, 50))..." -ForegroundColor Green
        if (-not $token) { $token = $loginResponse.data.token }
    } else {
        Write-Host "❌ data.token不存在" -ForegroundColor Red
    }
    
    # 3. 使用获得的token测试getInfo
    if ($token) {
        Write-Host "`n3. 测试getInfo接口（使用获得的token）..." -ForegroundColor Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        try {
            $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
            Write-Host "用户信息响应格式:" -ForegroundColor Cyan
            Write-Host ($userInfoResponse | ConvertTo-Json -Depth 3) -ForegroundColor White
            
            if ($userInfoResponse.code -eq 200) {
                Write-Host "✅ getInfo成功" -ForegroundColor Green
            } else {
                Write-Host "❌ getInfo失败: $($userInfoResponse.msg)" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ getInfo请求失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 4. 测试getRouters接口
        Write-Host "`n4. 测试getRouters接口..." -ForegroundColor Yellow
        try {
            $routersResponse = Invoke-RestMethod -Uri "$baseUrl/getRouters" -Method GET -Headers $headers
            Write-Host "路由响应格式:" -ForegroundColor Cyan
            Write-Host ($routersResponse | ConvertTo-Json -Depth 3) -ForegroundColor White
            
            if ($routersResponse.code -eq 200) {
                Write-Host "✅ getRouters成功" -ForegroundColor Green
            } else {
                Write-Host "❌ getRouters失败: $($routersResponse.msg)" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ getRouters请求失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 无法获得token，跳过后续测试" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 模拟前端可能的错误情况
Write-Host "`n5. 模拟前端可能的错误情况..." -ForegroundColor Yellow

# 测试undefined token
Write-Host "测试undefined token..." -ForegroundColor Cyan
try {
    $headers = @{
        "Authorization" = "Bearer undefined"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
    Write-Host "❌ 应该返回401，但返回了: $($response.code)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ 正确返回401未授权" -ForegroundColor Green
    } else {
        Write-Host "❌ 意外错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试空token
Write-Host "测试空token..." -ForegroundColor Cyan
try {
    $headers = @{
        "Authorization" = "Bearer "
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
    Write-Host "❌ 应该返回401，但返回了: $($response.code)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ 正确返回401未授权" -ForegroundColor Green
    } else {
        Write-Host "❌ 意外错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 前端兼容性测试完成!" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
