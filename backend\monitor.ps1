# WOSM Go Backend Performance Monitor
# 性能监控脚本

param(
    [int]$Duration = 60,      # 监控持续时间（秒）
    [int]$Interval = 5,       # 监控间隔（秒）
    [switch]$Continuous,      # 持续监控
    [switch]$SaveReport       # 保存报告
)

$ErrorActionPreference = "SilentlyContinue"

# 配置
$ServiceName = "wosm"
$Port = 8080
$BaseUrl = "http://localhost:$Port"
$ReportFile = "monitor-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Header($message) {
    $line = "=" * 60
    Write-ColorOutput Cyan $line
    Write-ColorOutput Cyan "  $message"
    Write-ColorOutput Cyan $line
}

function Write-Metric($name, $value, $unit = "") {
    $formatted = "{0,-25} : {1} {2}" -f $name, $value, $unit
    Write-ColorOutput Green $formatted
}

function Write-Alert($message) {
    Write-ColorOutput Red "⚠️  ALERT: $message"
}

# 获取进程信息
function Get-ProcessInfo {
    $process = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
    if ($process) {
        return @{
            PID = $process.Id
            CPU = $process.CPU
            WorkingSet = [math]::Round($process.WorkingSet64 / 1MB, 2)
            VirtualMemory = [math]::Round($process.VirtualMemorySize64 / 1MB, 2)
            Threads = $process.Threads.Count
            Handles = $process.HandleCount
            StartTime = $process.StartTime
        }
    }
    return $null
}

# 获取系统信息
function Get-SystemInfo {
    $cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    
    return @{
        CPUUsage = [math]::Round($cpu.Average, 2)
        TotalMemory = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
        FreeMemory = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
        MemoryUsage = [math]::Round((($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize) * 100, 2)
        DiskFree = [math]::Round($disk.FreeSpace / 1GB, 2)
        DiskTotal = [math]::Round($disk.Size / 1GB, 2)
        DiskUsage = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)
    }
}

# 获取网络信息
function Get-NetworkInfo {
    try {
        $connections = netstat -an | findstr ":$Port "
        $established = ($connections | findstr "ESTABLISHED").Count
        $listening = ($connections | findstr "LISTENING").Count
        $timeWait = ($connections | findstr "TIME_WAIT").Count
        
        return @{
            Established = $established
            Listening = $listening
            TimeWait = $timeWait
            Total = $connections.Count
        }
    } catch {
        return @{
            Established = 0
            Listening = 0
            TimeWait = 0
            Total = 0
        }
    }
}

# 性能测试
function Test-Performance {
    $results = @{
        HealthCheck = @{ Success = $false; ResponseTime = 0 }
        Login = @{ Success = $false; ResponseTime = 0 }
        UserList = @{ Success = $false; ResponseTime = 0 }
    }
    
    # 健康检查
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $health = Invoke-RestMethod -Uri "$BaseUrl/health" -Method Get -TimeoutSec 5
        $stopwatch.Stop()
        
        $results.HealthCheck.Success = $health.status -eq "up"
        $results.HealthCheck.ResponseTime = $stopwatch.ElapsedMilliseconds
    } catch {
        $results.HealthCheck.Success = $false
    }
    
    # 登录测试
    try {
        $loginBody = '{"username":"admin","password":"admin123"}'
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $loginResponse = Invoke-RestMethod -Uri "$BaseUrl/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
        $stopwatch.Stop()
        
        $results.Login.Success = $loginResponse.code -eq 200
        $results.Login.ResponseTime = $stopwatch.ElapsedMilliseconds
        
        if ($results.Login.Success) {
            $token = $loginResponse.data.token
            $headers = @{ Authorization = "Bearer $token" }
            
            # 用户列表测试
            try {
                $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
                $userList = Invoke-RestMethod -Uri "$BaseUrl/api/system/user/list" -Headers $headers -TimeoutSec 10
                $stopwatch.Stop()
                
                $results.UserList.Success = $userList.code -eq 200
                $results.UserList.ResponseTime = $stopwatch.ElapsedMilliseconds
            } catch {
                $results.UserList.Success = $false
            }
        }
    } catch {
        $results.Login.Success = $false
    }
    
    return $results
}

# 生成报告
function Generate-Report($data) {
    $report = @"
WOSM Go Backend 性能监控报告
生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
监控持续时间: $Duration 秒
监控间隔: $Interval 秒

=== 服务状态 ===
服务运行: $($data.ServiceRunning)
监控周期数: $($data.Cycles)

=== 平均性能指标 ===
CPU使用率: $($data.AvgCPU)%
内存使用: $($data.AvgMemory) MB
线程数: $($data.AvgThreads)
句柄数: $($data.AvgHandles)

=== 响应时间 (毫秒) ===
健康检查: $($data.AvgHealthTime) ms
登录接口: $($data.AvgLoginTime) ms
用户列表: $($data.AvgUserListTime) ms

=== 网络连接 ===
平均活跃连接: $($data.AvgConnections)
平均等待连接: $($data.AvgTimeWait)

=== 系统资源 ===
系统CPU: $($data.AvgSystemCPU)%
系统内存使用: $($data.AvgSystemMemory)%
磁盘使用: $($data.AvgDiskUsage)%

=== 告警信息 ===
$($data.Alerts -join "`n")

"@
    
    if ($SaveReport) {
        $report | Out-File -FilePath $ReportFile -Encoding UTF8
        Write-ColorOutput Yellow "报告已保存到: $ReportFile"
    }
    
    return $report
}

# 主监控循环
function Start-Monitoring {
    Write-Header "WOSM Go Backend 性能监控"
    Write-ColorOutput Yellow "监控持续时间: $Duration 秒"
    Write-ColorOutput Yellow "监控间隔: $Interval 秒"
    Write-ColorOutput Yellow "按 Ctrl+C 停止监控"
    Write-Host ""
    
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($Duration)
    $cycle = 0
    
    # 数据收集
    $data = @{
        ServiceRunning = $true
        Cycles = 0
        CPUValues = @()
        MemoryValues = @()
        ThreadValues = @()
        HandleValues = @()
        HealthTimes = @()
        LoginTimes = @()
        UserListTimes = @()
        ConnectionValues = @()
        TimeWaitValues = @()
        SystemCPUValues = @()
        SystemMemoryValues = @()
        DiskUsageValues = @()
        Alerts = @()
    }
    
    do {
        $cycle++
        $currentTime = Get-Date
        
        Write-Host "[$($currentTime.ToString('HH:mm:ss'))] 监控周期 #$cycle" -ForegroundColor Cyan
        
        # 获取进程信息
        $processInfo = Get-ProcessInfo
        if ($processInfo) {
            Write-Metric "PID" $processInfo.PID
            Write-Metric "CPU时间" "$($processInfo.CPU) 秒"
            Write-Metric "内存使用" "$($processInfo.WorkingSet) MB"
            Write-Metric "虚拟内存" "$($processInfo.VirtualMemory) MB"
            Write-Metric "线程数" $processInfo.Threads
            Write-Metric "句柄数" $processInfo.Handles
            
            $data.CPUValues += $processInfo.CPU
            $data.MemoryValues += $processInfo.WorkingSet
            $data.ThreadValues += $processInfo.Threads
            $data.HandleValues += $processInfo.Handles
            
            # 检查告警条件
            if ($processInfo.WorkingSet -gt 500) {
                $alert = "高内存使用: $($processInfo.WorkingSet) MB"
                $data.Alerts += $alert
                Write-Alert $alert
            }
            
            if ($processInfo.Threads -gt 100) {
                $alert = "高线程数: $($processInfo.Threads)"
                $data.Alerts += $alert
                Write-Alert $alert
            }
        } else {
            Write-ColorOutput Red "❌ 服务未运行"
            $data.ServiceRunning = $false
        }
        
        # 获取系统信息
        $systemInfo = Get-SystemInfo
        Write-Metric "系统CPU" "$($systemInfo.CPUUsage)%"
        Write-Metric "系统内存" "$($systemInfo.MemoryUsage)%"
        Write-Metric "磁盘使用" "$($systemInfo.DiskUsage)%"
        
        $data.SystemCPUValues += $systemInfo.CPUUsage
        $data.SystemMemoryValues += $systemInfo.MemoryUsage
        $data.DiskUsageValues += $systemInfo.DiskUsage
        
        # 获取网络信息
        $networkInfo = Get-NetworkInfo
        Write-Metric "活跃连接" $networkInfo.Established
        Write-Metric "等待连接" $networkInfo.TimeWait
        
        $data.ConnectionValues += $networkInfo.Established
        $data.TimeWaitValues += $networkInfo.TimeWait
        
        # 性能测试
        if ($processInfo) {
            $perfResults = Test-Performance
            
            if ($perfResults.HealthCheck.Success) {
                Write-Metric "健康检查" "$($perfResults.HealthCheck.ResponseTime) ms" "✅"
                $data.HealthTimes += $perfResults.HealthCheck.ResponseTime
            } else {
                Write-ColorOutput Red "❌ 健康检查失败"
            }
            
            if ($perfResults.Login.Success) {
                Write-Metric "登录响应" "$($perfResults.Login.ResponseTime) ms" "✅"
                $data.LoginTimes += $perfResults.Login.ResponseTime
            } else {
                Write-ColorOutput Red "❌ 登录测试失败"
            }
            
            if ($perfResults.UserList.Success) {
                Write-Metric "用户列表" "$($perfResults.UserList.ResponseTime) ms" "✅"
                $data.UserListTimes += $perfResults.UserList.ResponseTime
            } else {
                Write-ColorOutput Red "❌ 用户列表测试失败"
            }
            
            # 响应时间告警
            if ($perfResults.HealthCheck.ResponseTime -gt 1000) {
                $alert = "健康检查响应慢: $($perfResults.HealthCheck.ResponseTime) ms"
                $data.Alerts += $alert
                Write-Alert $alert
            }
        }
        
        $data.Cycles = $cycle
        
        Write-Host ""
        
        if (-not $Continuous -and $currentTime -ge $endTime) {
            break
        }
        
        Start-Sleep -Seconds $Interval
        
    } while ($Continuous -or $currentTime -lt $endTime)
    
    # 计算平均值
    if ($data.CPUValues.Count -gt 0) {
        $data.AvgCPU = [math]::Round(($data.CPUValues | Measure-Object -Average).Average, 2)
        $data.AvgMemory = [math]::Round(($data.MemoryValues | Measure-Object -Average).Average, 2)
        $data.AvgThreads = [math]::Round(($data.ThreadValues | Measure-Object -Average).Average, 0)
        $data.AvgHandles = [math]::Round(($data.HandleValues | Measure-Object -Average).Average, 0)
    }
    
    if ($data.HealthTimes.Count -gt 0) {
        $data.AvgHealthTime = [math]::Round(($data.HealthTimes | Measure-Object -Average).Average, 0)
    }
    
    if ($data.LoginTimes.Count -gt 0) {
        $data.AvgLoginTime = [math]::Round(($data.LoginTimes | Measure-Object -Average).Average, 0)
    }
    
    if ($data.UserListTimes.Count -gt 0) {
        $data.AvgUserListTime = [math]::Round(($data.UserListTimes | Measure-Object -Average).Average, 0)
    }
    
    if ($data.ConnectionValues.Count -gt 0) {
        $data.AvgConnections = [math]::Round(($data.ConnectionValues | Measure-Object -Average).Average, 0)
        $data.AvgTimeWait = [math]::Round(($data.TimeWaitValues | Measure-Object -Average).Average, 0)
    }
    
    if ($data.SystemCPUValues.Count -gt 0) {
        $data.AvgSystemCPU = [math]::Round(($data.SystemCPUValues | Measure-Object -Average).Average, 2)
        $data.AvgSystemMemory = [math]::Round(($data.SystemMemoryValues | Measure-Object -Average).Average, 2)
        $data.AvgDiskUsage = [math]::Round(($data.DiskUsageValues | Measure-Object -Average).Average, 2)
    }
    
    # 生成报告
    Write-Header "监控报告"
    $report = Generate-Report $data
    Write-Host $report
}

# 启动监控
Start-Monitoring
