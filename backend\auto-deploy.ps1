# WOSM Go Backend Auto Deploy Script
# 自动化部署和监控脚本

param(
    [string]$Action = "deploy",  # deploy, start, stop, restart, status, logs
    [string]$Environment = "prod"
)

$ErrorActionPreference = "Stop"

# 颜色定义
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Blue "========================================="
    Write-ColorOutput Blue "  $message"
    Write-ColorOutput Blue "========================================="
    Write-Host ""
}

# 配置
$ServiceName = "WOSM-Go-Backend"
$BinaryName = "wosm.exe"
$Port = 8080
$HealthCheckUrl = "http://localhost:$Port/health"
$LogFile = "logs/ruoyi.log"
$PidFile = "wosm.pid"

# 检查服务状态
function Get-ServiceStatus {
    try {
        $response = Invoke-RestMethod -Uri $HealthCheckUrl -Method Get -TimeoutSec 3
        if ($response.status -eq "up") {
            return @{
                Running = $true
                Status = "Healthy"
                Version = $response.version
                Service = $response.service
            }
        }
    } catch {
        # 检查进程是否存在
        $process = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
        if ($process) {
            return @{
                Running = $true
                Status = "Unhealthy"
                PID = $process.Id
            }
        }
    }
    
    return @{
        Running = $false
        Status = "Stopped"
    }
}

# 启动服务
function Start-Service {
    Write-Info "启动 $ServiceName 服务..."
    
    $status = Get-ServiceStatus
    if ($status.Running) {
        Write-Warning "服务已在运行中 (状态: $($status.Status))"
        return
    }
    
    # 检查二进制文件
    if (-not (Test-Path $BinaryName)) {
        Write-Error "未找到可执行文件: $BinaryName"
        Write-Info "请先运行构建: go build -o $BinaryName main.go"
        exit 1
    }
    
    # 启动服务
    $process = Start-Process -FilePath ".\$BinaryName" -PassThru -WindowStyle Hidden
    
    # 等待服务启动
    Write-Info "等待服务启动..."
    $maxWait = 30
    $waited = 0
    
    while ($waited -lt $maxWait) {
        Start-Sleep -Seconds 1
        $waited++
        
        $status = Get-ServiceStatus
        if ($status.Running -and $status.Status -eq "Healthy") {
            Write-Success "服务启动成功!"
            Write-Info "PID: $($process.Id)"
            Write-Info "版本: $($status.Version)"
            Write-Info "端口: $Port"
            
            # 保存PID
            $process.Id | Out-File -FilePath $PidFile -Encoding UTF8
            return
        }
        
        Write-Host "." -NoNewline
    }
    
    Write-Error "服务启动超时"
    exit 1
}

# 停止服务
function Stop-Service {
    Write-Info "停止 $ServiceName 服务..."
    
    $status = Get-ServiceStatus
    if (-not $status.Running) {
        Write-Warning "服务未运行"
        return
    }
    
    # 尝试优雅停止
    try {
        $processes = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
        foreach ($process in $processes) {
            Write-Info "停止进程 PID: $($process.Id)"
            $process.CloseMainWindow()
            
            # 等待进程结束
            if (-not $process.WaitForExit(10000)) {
                Write-Warning "强制终止进程"
                $process.Kill()
            }
        }
        
        Write-Success "服务已停止"
        
        # 清理PID文件
        if (Test-Path $PidFile) {
            Remove-Item $PidFile
        }
        
    } catch {
        Write-Error "停止服务失败: $_"
        exit 1
    }
}

# 重启服务
function Restart-Service {
    Write-Header "重启 $ServiceName"
    Stop-Service
    Start-Sleep -Seconds 2
    Start-Service
}

# 显示服务状态
function Show-Status {
    Write-Header "$ServiceName 状态"
    
    $status = Get-ServiceStatus
    
    if ($status.Running) {
        if ($status.Status -eq "Healthy") {
            Write-Success "服务运行正常"
            Write-Info "服务: $($status.Service)"
            Write-Info "版本: $($status.Version)"
            Write-Info "端口: $Port"
        } else {
            Write-Warning "服务运行异常"
            if ($status.PID) {
                Write-Info "PID: $($status.PID)"
            }
        }
    } else {
        Write-Warning "服务未运行"
    }
    
    # 显示端口占用情况
    try {
        $portInfo = netstat -ano | findstr ":$Port "
        if ($portInfo) {
            Write-Info "端口占用情况:"
            $portInfo | ForEach-Object { Write-Host "  $_" }
        }
    } catch {
        # 忽略错误
    }
}

# 显示日志
function Show-Logs {
    param([int]$Lines = 50)
    
    Write-Header "服务日志 (最近 $Lines 行)"
    
    if (Test-Path $LogFile) {
        Get-Content $LogFile -Tail $Lines | ForEach-Object {
            if ($_ -match "ERROR|FATAL") {
                Write-ColorOutput Red $_
            } elseif ($_ -match "WARN") {
                Write-ColorOutput Yellow $_
            } elseif ($_ -match "INFO") {
                Write-ColorOutput Green $_
            } else {
                Write-Host $_
            }
        }
    } else {
        Write-Warning "日志文件不存在: $LogFile"
    }
}

# 部署服务
function Deploy-Service {
    Write-Header "部署 $ServiceName"
    
    # 停止现有服务
    Stop-Service
    
    # 构建应用
    Write-Info "构建应用..."
    try {
        go build -o $BinaryName main.go
        Write-Success "构建完成"
    } catch {
        Write-Error "构建失败: $_"
        exit 1
    }
    
    # 启动服务
    Start-Service
    
    # 运行健康检查
    Write-Info "运行健康检查..."
    Start-Sleep -Seconds 3
    
    try {
        $health = Invoke-RestMethod -Uri $HealthCheckUrl -Method Get -TimeoutSec 10
        Write-Success "健康检查通过"
        Write-Info "服务: $($health.service)"
        Write-Info "版本: $($health.version)"
        Write-Info "状态: $($health.status)"
    } catch {
        Write-Error "健康检查失败: $_"
        exit 1
    }
    
    Write-Success "部署完成!"
}

# 主逻辑
switch ($Action.ToLower()) {
    "deploy" {
        Deploy-Service
    }
    "start" {
        Write-Header "启动 $ServiceName"
        Start-Service
    }
    "stop" {
        Write-Header "停止 $ServiceName"
        Stop-Service
    }
    "restart" {
        Restart-Service
    }
    "status" {
        Show-Status
    }
    "logs" {
        Show-Logs
    }
    default {
        Write-Header "WOSM Go Backend 管理脚本"
        Write-Host "用法: .\auto-deploy.ps1 [Action] [Environment]"
        Write-Host ""
        Write-Host "Actions:"
        Write-Host "  deploy   - 部署服务 (构建 + 启动 + 健康检查)"
        Write-Host "  start    - 启动服务"
        Write-Host "  stop     - 停止服务"
        Write-Host "  restart  - 重启服务"
        Write-Host "  status   - 显示服务状态"
        Write-Host "  logs     - 显示服务日志"
        Write-Host ""
        Write-Host "Environment: dev, test, prod (默认: prod)"
        Write-Host ""
        Write-Host "示例:"
        Write-Host "  .\auto-deploy.ps1 deploy prod"
        Write-Host "  .\auto-deploy.ps1 status"
        Write-Host "  .\auto-deploy.ps1 logs"
    }
}
