# 前端集成问题修复指南

## 🔍 问题诊断

根据服务器日志分析，发现前端发送的Authorization头是 `"Bearer undefined"`，说明前端没有正确保存或发送JWT令牌。

### 问题日志
```
收到getInfo请求 {"Authorization": "Bearer undefined"}
JWT令牌验证失败 {"error": "token contains an invalid number of segments"}
```

## 🎯 问题根源

1. **后端完全正常** - PowerShell测试证明所有接口工作正常
2. **前端JavaScript问题** - 没有正确处理登录响应中的token

## 🔧 解决方案

### 1. 检查登录响应格式

后端登录接口返回的数据格式：
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "userId": 1,
      "userName": "管理员",
      "loginName": "admin",
      "email": "<EMAIL>"
    }
  }
}
```

### 2. 前端JavaScript修复

#### 修复登录处理代码

**错误的代码（可能导致undefined）：**
```javascript
// 错误示例 - 可能导致token为undefined
fetch('/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(loginData)
})
.then(response => response.json())
.then(data => {
  // 错误：直接使用data.token而不是data.data.token
  localStorage.setItem('token', data.token); // undefined!
});
```

**正确的代码：**
```javascript
// 正确的登录处理
fetch('/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'admin123',
    code: '',
    uuid: ''
  })
})
.then(response => response.json())
.then(data => {
  console.log('登录响应:', data); // 调试用
  
  if (data.code === 200 && data.data && data.data.token) {
    // 正确：使用data.data.token
    localStorage.setItem('token', data.data.token);
    console.log('Token已保存:', data.data.token);
    
    // 保存用户信息
    localStorage.setItem('userInfo', JSON.stringify(data.data.user));
    
    // 跳转到主页或刷新页面
    window.location.href = '/index.html';
  } else {
    console.error('登录失败:', data.msg);
    alert('登录失败: ' + data.msg);
  }
})
.catch(error => {
  console.error('登录请求失败:', error);
  alert('登录请求失败: ' + error.message);
});
```

#### 修复请求拦截器

**确保所有API请求都正确发送token：**
```javascript
// 请求拦截器 - 自动添加Authorization头
function makeAuthenticatedRequest(url, options = {}) {
  const token = localStorage.getItem('token');
  
  if (!token) {
    console.warn('没有找到token，可能需要重新登录');
    // 可以选择跳转到登录页
    // window.location.href = '/login.html';
    return Promise.reject(new Error('未找到认证令牌'));
  }
  
  // 确保headers存在
  options.headers = options.headers || {};
  
  // 添加Authorization头
  options.headers['Authorization'] = `Bearer ${token}`;
  options.headers['Content-Type'] = 'application/json';
  
  console.log('发送请求:', url, '带token:', token.substring(0, 50) + '...');
  
  return fetch(url, options)
    .then(response => {
      if (response.status === 401) {
        console.warn('认证失败，清除token并跳转登录');
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        window.location.href = '/login.html';
        return Promise.reject(new Error('认证失败'));
      }
      return response.json();
    });
}

// 使用示例
makeAuthenticatedRequest('/getInfo')
  .then(data => {
    if (data.code === 200) {
      console.log('用户信息:', data.data.user);
    }
  })
  .catch(error => {
    console.error('获取用户信息失败:', error);
  });
```

### 3. 调试步骤

#### 步骤1：检查登录响应
在浏览器开发者工具中：
```javascript
// 在控制台中测试登录
fetch('/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'admin123',
    code: '',
    uuid: ''
  })
})
.then(response => response.json())
.then(data => {
  console.log('完整响应:', data);
  console.log('Token路径:', data.data ? data.data.token : '未找到');
});
```

#### 步骤2：检查token存储
```javascript
// 检查localStorage中的token
console.log('存储的token:', localStorage.getItem('token'));
console.log('token类型:', typeof localStorage.getItem('token'));
```

#### 步骤3：检查请求头
在Network标签中查看 `/getInfo` 请求的Headers，确认：
- `Authorization: Bearer <实际的JWT令牌>`
- 不应该是 `Authorization: Bearer undefined`

### 4. 完整的前端示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>WOSM 登录测试</title>
</head>
<body>
    <div id="login-form">
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="login()">登录</button>
    </div>
    
    <div id="user-info" style="display:none;">
        <h3>用户信息</h3>
        <div id="user-details"></div>
        <button onclick="getUserInfo()">获取用户信息</button>
        <button onclick="logout()">登出</button>
    </div>

    <script>
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            fetch('/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    code: '',
                    uuid: ''
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('登录响应:', data);
                
                if (data.code === 200 && data.data && data.data.token) {
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.data.user));
                    
                    document.getElementById('login-form').style.display = 'none';
                    document.getElementById('user-info').style.display = 'block';
                    
                    alert('登录成功！');
                } else {
                    alert('登录失败: ' + data.msg);
                }
            })
            .catch(error => {
                console.error('登录错误:', error);
                alert('登录请求失败');
            });
        }
        
        function getUserInfo() {
            const token = localStorage.getItem('token');
            
            if (!token) {
                alert('未找到token，请重新登录');
                return;
            }
            
            fetch('/getInfo', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('用户信息响应:', data);
                
                if (data.code === 200) {
                    document.getElementById('user-details').innerHTML = 
                        `<p>用户名: ${data.data.user.loginName}</p>
                         <p>姓名: ${data.data.user.userName}</p>
                         <p>邮箱: ${data.data.user.email}</p>`;
                } else {
                    alert('获取用户信息失败: ' + data.msg);
                }
            })
            .catch(error => {
                console.error('获取用户信息错误:', error);
                alert('请求失败');
            });
        }
        
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            
            document.getElementById('login-form').style.display = 'block';
            document.getElementById('user-info').style.display = 'none';
            
            alert('已登出');
        }
    </script>
</body>
</html>
```

## 🎯 关键要点

1. **正确的token路径**: `response.data.token`（不是`response.token`）
2. **正确的Authorization头**: `Bearer ${token}`（确保token不是undefined）
3. **错误处理**: 检查响应状态和token存在性
4. **调试日志**: 使用console.log查看实际的响应和token值

## 🔍 故障排除

如果问题仍然存在：

1. **检查浏览器控制台** - 查看JavaScript错误
2. **检查Network标签** - 查看实际发送的请求头
3. **检查localStorage** - 确认token正确存储
4. **检查后端日志** - 确认收到的Authorization头内容

按照这个指南修复前端代码后，JWT认证应该能正常工作。
