# 测试登录响应格式
Write-Host "测试登录响应格式..." -ForegroundColor Green

$loginData = @{
    username = "admin"
    password = "admin123"
    code = ""
    uuid = ""
} | ConvertTo-<PERSON><PERSON>

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "登录响应:" -ForegroundColor Yellow
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
    
    if ($response.data -and $response.data.token) {
        Write-Host "`n令牌路径正确: response.data.token" -ForegroundColor Green
        Write-Host "令牌值: $($response.data.token)" -ForegroundColor Cyan
    } else {
        Write-Host "`n警告: 令牌路径可能不正确" -ForegroundColor Red
        Write-Host "响应结构:" -ForegroundColor Yellow
        $response | Get-Member | Format-Table
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}
