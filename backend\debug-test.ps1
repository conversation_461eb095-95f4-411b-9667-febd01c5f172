# Debug Test for Failed Integration Tests

Write-Host "=== Debug Test for Failed Integration Tests ===" -ForegroundColor Blue

# Get authentication token
$loginBody = '{"username":"admin","password":"admin123"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
$token = $loginResponse.data.token
$headers = @{ Authorization = "Bearer $token" }

Write-Host "Token obtained successfully" -ForegroundColor Green

# Test 1: Department Creation Debug
Write-Host ""
Write-Host "1. Testing Department Creation..." -ForegroundColor Yellow
try {
    $deptBody = @{
        deptName = "Test Department"
        parentId = 100
        orderNum = 1
        leader = "Test Leader"
        phone = "13800138000"
        email = "<EMAIL>"
        status = "0"
    } | ConvertTo-Json
    
    Write-Host "Department Body: $deptBody" -ForegroundColor Gray
    
    $deptResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept" -Method Post -Body $deptBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Department Response: $($deptResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Department Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Role Creation Debug
Write-Host ""
Write-Host "2. Testing Role Creation..." -ForegroundColor Yellow
try {
    $roleBody = @{
        roleName = "Test Role"
        roleKey = "test_role"
        roleSort = 1
        status = "0"
        menuIds = @(1, 2, 3)
        deptIds = @(100)
        remark = "Test role for integration testing"
    } | ConvertTo-Json
    
    Write-Host "Role Body: $roleBody" -ForegroundColor Gray
    
    $roleResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/role" -Method Post -Body $roleBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Role Response: $($roleResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Role Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check existing departments
Write-Host ""
Write-Host "3. Checking existing departments..." -ForegroundColor Yellow
try {
    $deptListResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept/list" -Headers $headers -TimeoutSec 10
    Write-Host "Department List Response: $($deptListResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "Department List Error: $_" -ForegroundColor Red
}

# Test 4: Check existing roles
Write-Host ""
Write-Host "4. Checking existing roles..." -ForegroundColor Yellow
try {
    $roleListResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/role/list" -Headers $headers -TimeoutSec 10
    Write-Host "Role List Response: $($roleListResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "Role List Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug test completed!" -ForegroundColor Blue
