# 简单的JWT测试脚本
Write-Host "JWT认证测试开始..." -ForegroundColor Green

$baseUrl = "http://localhost:8080"

# 1. 测试健康检查
Write-Host "1. 测试健康检查..." -ForegroundColor Yellow
$healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
Write-Host "健康检查结果: $($healthResponse | ConvertTo-Json)" -ForegroundColor Green

# 2. 测试登录
Write-Host "2. 测试登录..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123"
    code = ""
    uuid = ""
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
Write-Host "登录结果: $($loginResponse.code) - $($loginResponse.msg)" -ForegroundColor Green

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "获得Token: $($token.Substring(0, 50))..." -ForegroundColor Cyan
    
    # 3. 测试获取用户信息
    Write-Host "3. 测试获取用户信息..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    try {
        $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
        Write-Host "用户信息结果: $($userInfoResponse.code) - $($userInfoResponse.msg)" -ForegroundColor Green
        if ($userInfoResponse.code -eq 200) {
            Write-Host "用户名: $($userInfoResponse.data.user.loginName)" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "测试完成!" -ForegroundColor Green
