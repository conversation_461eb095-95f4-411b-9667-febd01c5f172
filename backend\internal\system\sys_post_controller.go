package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysPostController 岗位信息控制器
type SysPostController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysPostController 创建岗位控制器
func NewSysPostController(logger *zap.Logger, db *gorm.DB) *SysPostController {
	return &SysPostController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysPostController的路由
func (c *SysPostController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                 // 岗位列表
	r.POST("/export", c.Export)            // 导出岗位
	r.GET("/:postId", c.GetInfo)           // 获取岗位详情
	r.POST("", c.Add)                      // 新增岗位
	r.PUT("", c.Edit)                      // 修改岗位
	r.DELETE("/:postIds", c.Remove)        // 删除岗位
	r.GET("/optionselect", c.Optionselect) // 岗位选择框
}

// List 获取岗位列表 - 完全按照Java后端业务逻辑
func (c *SysPostController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var posts []domain.SysPost
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysPost{}).Where("del_flag = ?", "0")

	// 岗位编码查询
	if postCode := ctx.Query("postCode"); postCode != "" {
		query = query.Where("post_code LIKE ?", "%"+postCode+"%")
	}

	// 岗位名称查询
	if postName := ctx.Query("postName"); postName != "" {
		query = query.Where("post_name LIKE ?", "%"+postName+"%")
	}

	// 岗位状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("post_sort ASC, post_id ASC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&posts).Error; err != nil {
		c.Logger.Error("查询岗位列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  posts,
	})
}

// GetInfo 根据岗位编号获取详细信息 - 完全按照Java后端逻辑
func (c *SysPostController) GetInfo(ctx *gin.Context) {
	postIdStr := ctx.Param("postId")
	postId, err := strconv.ParseInt(postIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "岗位ID格式错误")
		return
	}

	var post domain.SysPost
	if err := c.db.Where("post_id = ? AND del_flag = ?", postId, "0").First(&post).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "岗位不存在")
		} else {
			c.Logger.Error("查询岗位信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, post)
}

// Add 新增岗位 - 完全按照Java后端业务逻辑
func (c *SysPostController) Add(ctx *gin.Context) {
	var post domain.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if post.PostCode == "" {
		c.ErrorWithMessage(ctx, "岗位编码不能为空")
		return
	}
	if post.PostName == "" {
		c.ErrorWithMessage(ctx, "岗位名称不能为空")
		return
	}
	if len(post.PostCode) > 64 {
		c.ErrorWithMessage(ctx, "岗位编码长度不能超过64个字符")
		return
	}
	if len(post.PostName) > 50 {
		c.ErrorWithMessage(ctx, "岗位名称长度不能超过50个字符")
		return
	}

	// 检查岗位名称唯一性
	if !c.checkPostNameUnique(&post) {
		c.ErrorWithMessage(ctx, "新增岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	}

	// 检查岗位编码唯一性
	if !c.checkPostCodeUnique(&post) {
		c.ErrorWithMessage(ctx, "新增岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置默认值
	post.CreateBy = c.GetUsername(ctx)
	if post.Status == "" {
		post.Status = "0" // 默认正常状态
	}
	if post.DelFlag == "" {
		post.DelFlag = "0" // 默认未删除
	}

	// 保存岗位
	if err := c.db.Create(&post).Error; err != nil {
		c.Logger.Error("新增岗位失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改岗位 - 完全按照Java后端业务逻辑
func (c *SysPostController) Edit(ctx *gin.Context) {
	var post domain.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if post.PostId == 0 {
		c.ErrorWithMessage(ctx, "岗位ID不能为空")
		return
	}
	if post.PostCode == "" {
		c.ErrorWithMessage(ctx, "岗位编码不能为空")
		return
	}
	if post.PostName == "" {
		c.ErrorWithMessage(ctx, "岗位名称不能为空")
		return
	}
	if len(post.PostCode) > 64 {
		c.ErrorWithMessage(ctx, "岗位编码长度不能超过64个字符")
		return
	}
	if len(post.PostName) > 50 {
		c.ErrorWithMessage(ctx, "岗位名称长度不能超过50个字符")
		return
	}

	// 检查岗位是否存在
	var existingPost domain.SysPost
	if err := c.db.Where("post_id = ? AND del_flag = ?", post.PostId, "0").First(&existingPost).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "岗位不存在")
		} else {
			c.Logger.Error("查询岗位失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查岗位名称唯一性（排除自己）
	if !c.checkPostNameUnique(&post) {
		c.ErrorWithMessage(ctx, "修改岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	}

	// 检查岗位编码唯一性（排除自己）
	if !c.checkPostCodeUnique(&post) {
		c.ErrorWithMessage(ctx, "修改岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置更新者
	post.UpdateBy = c.GetUsername(ctx)

	// 更新岗位信息
	updateData := map[string]interface{}{
		"post_code": post.PostCode,
		"post_name": post.PostName,
		"post_sort": post.PostSort,
		"status":    post.Status,
		"update_by": post.UpdateBy,
		"remark":    post.Remark,
	}

	if err := c.db.Model(&existingPost).Updates(updateData).Error; err != nil {
		c.Logger.Error("修改岗位失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除岗位 - 完全按照Java后端业务逻辑
func (c *SysPostController) Remove(ctx *gin.Context) {
	postIdsStr := ctx.Param("postIds")
	postIdStrs := strings.Split(postIdsStr, ",")

	var postIds []int64
	for _, postIdStr := range postIdStrs {
		postId, err := strconv.ParseInt(postIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "岗位ID格式错误")
			return
		}
		postIds = append(postIds, postId)
	}

	// 检查岗位是否被用户使用
	for _, postId := range postIds {
		var count int64
		if err := c.db.Model(&domain.SysUserPost{}).Where("post_id = ?", postId).Count(&count).Error; err != nil {
			c.Logger.Error("查询岗位使用情况失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
			return
		}
		if count > 0 {
			c.ErrorWithMessage(ctx, "岗位已分配用户，不能删除")
			return
		}
	}

	// 软删除岗位
	if err := c.db.Model(&domain.SysPost{}).Where("post_id IN ?", postIds).Update("del_flag", "2").Error; err != nil {
		c.Logger.Error("删除岗位失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// Optionselect 获取岗位选择框列表 - 完全按照Java后端业务逻辑
func (c *SysPostController) Optionselect(ctx *gin.Context) {
	var posts []domain.SysPost

	// 查询所有正常状态的岗位
	if err := c.db.Model(&domain.SysPost{}).Where("del_flag = ? AND status = ?", "0", "0").Order("post_sort ASC, post_id ASC").Find(&posts).Error; err != nil {
		c.Logger.Error("查询岗位选择框列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	c.SuccessWithData(ctx, posts)
}

// Export 导出岗位 - 简化实现
func (c *SysPostController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 辅助函数

// checkPostNameUnique 校验岗位名称是否唯一
func (c *SysPostController) checkPostNameUnique(post *domain.SysPost) bool {
	var count int64
	query := c.db.Model(&domain.SysPost{}).Where("post_name = ? AND del_flag = ?", post.PostName, "0")

	// 如果是修改操作，排除自己
	if post.PostId > 0 {
		query = query.Where("post_id != ?", post.PostId)
	}

	query.Count(&count)
	return count == 0
}

// checkPostCodeUnique 校验岗位编码是否唯一
func (c *SysPostController) checkPostCodeUnique(post *domain.SysPost) bool {
	var count int64
	query := c.db.Model(&domain.SysPost{}).Where("post_code = ? AND del_flag = ?", post.PostCode, "0")

	// 如果是修改操作，排除自己
	if post.PostId > 0 {
		query = query.Where("post_id != ?", post.PostId)
	}

	query.Count(&count)
	return count == 0
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysPostController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysPostController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysPostController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysPostController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
