# WOSM Extended API Test Script
# 测试新增的API接口

Write-Host "=== WOSM Extended API Test ===" -ForegroundColor Blue
Write-Host ""

# 获取认证令牌
Write-Host "1. Getting authentication token..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    $token = $loginResponse.data.token
    $headers = @{ Authorization = "Bearer $token" }
    Write-Host "✅ Token obtained successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get token: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试部门管理API
Write-Host "2. Testing Department Management APIs..." -ForegroundColor Yellow

# 部门列表
try {
    $deptList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept/list" -Headers $headers -TimeoutSec 10
    if ($deptList.code -eq 200) {
        Write-Host "✅ Department List API - Success" -ForegroundColor Green
        Write-Host "   Total Departments: $($deptList.data.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Department List API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Department List API - Error: $_" -ForegroundColor Red
}

# 部门详情
try {
    $deptDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept/103" -Headers $headers -TimeoutSec 10
    if ($deptDetail.code -eq 200) {
        Write-Host "✅ Department Detail API - Success" -ForegroundColor Green
        Write-Host "   Department: $($deptDetail.data.deptName)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Department Detail API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Department Detail API - Error: $_" -ForegroundColor Red
}

# 排除部门列表
try {
    $deptExclude = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept/list/exclude/103" -Headers $headers -TimeoutSec 10
    if ($deptExclude.code -eq 200) {
        Write-Host "✅ Department Exclude API - Success" -ForegroundColor Green
    } else {
        Write-Host "❌ Department Exclude API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Department Exclude API - Error: $_" -ForegroundColor Red
}

Write-Host ""

# 测试岗位管理API
Write-Host "3. Testing Post Management APIs..." -ForegroundColor Yellow

# 岗位列表
try {
    $postList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/post/list" -Headers $headers -TimeoutSec 10
    if ($postList.code -eq 200) {
        Write-Host "✅ Post List API - Success" -ForegroundColor Green
        Write-Host "   Total Posts: $($postList.data.total)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Post List API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Post List API - Error: $_" -ForegroundColor Red
}

# 岗位详情
try {
    $postDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/post/1" -Headers $headers -TimeoutSec 10
    if ($postDetail.code -eq 200) {
        Write-Host "✅ Post Detail API - Success" -ForegroundColor Green
        Write-Host "   Post: $($postDetail.data.postName)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Post Detail API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Post Detail API - Error: $_" -ForegroundColor Red
}

Write-Host ""

# 测试字典类型管理API
Write-Host "4. Testing Dictionary Type Management APIs..." -ForegroundColor Yellow

# 字典类型列表
try {
    $dictTypeList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type/list" -Headers $headers -TimeoutSec 10
    if ($dictTypeList.code -eq 200) {
        Write-Host "✅ Dict Type List API - Success" -ForegroundColor Green
        Write-Host "   Total Dict Types: $($dictTypeList.data.total)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Dict Type List API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Dict Type List API - Error: $_" -ForegroundColor Red
}

# 字典类型详情
try {
    $dictTypeDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type/1" -Headers $headers -TimeoutSec 10
    if ($dictTypeDetail.code -eq 200) {
        Write-Host "✅ Dict Type Detail API - Success" -ForegroundColor Green
        Write-Host "   Dict Type: $($dictTypeDetail.data.dictName)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Dict Type Detail API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Dict Type Detail API - Error: $_" -ForegroundColor Red
}

# 字典类型选项
try {
    $dictTypeOptions = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type/optionselect" -Headers $headers -TimeoutSec 10
    if ($dictTypeOptions.code -eq 200) {
        Write-Host "✅ Dict Type Options API - Success" -ForegroundColor Green
        Write-Host "   Available Options: $($dictTypeOptions.data.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Dict Type Options API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Dict Type Options API - Error: $_" -ForegroundColor Red
}

Write-Host ""

# 测试CRUD操作
Write-Host "5. Testing CRUD Operations..." -ForegroundColor Yellow

# 测试部门新增
try {
    $newDeptJson = '{"parentId":100,"deptName":"测试部门","orderNum":5,"leader":"测试负责人","phone":"15888888888","email":"<EMAIL>","status":"0"}'
    $createDeptResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept" -Method Post -Body $newDeptJson -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($createDeptResult.code -eq 200) {
        Write-Host "✅ Department Create API - Success" -ForegroundColor Green
    } else {
        Write-Host "❌ Department Create API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Department Create API - Error: $_" -ForegroundColor Red
}

# 测试岗位新增
try {
    $newPostJson = '{"postCode":"test","postName":"测试岗位","postSort":5,"status":"0"}'
    $createPostResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/post" -Method Post -Body $newPostJson -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($createPostResult.code -eq 200) {
        Write-Host "✅ Post Create API - Success" -ForegroundColor Green
    } else {
        Write-Host "❌ Post Create API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Post Create API - Error: $_" -ForegroundColor Red
}

# 测试字典类型新增
try {
    $newDictTypeJson = '{"dictName":"测试字典","dictType":"test_dict","status":"0","remark":"测试用字典类型"}'
    $createDictTypeResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type" -Method Post -Body $newDictTypeJson -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($createDictTypeResult.code -eq 200) {
        Write-Host "✅ Dict Type Create API - Success" -ForegroundColor Green
    } else {
        Write-Host "❌ Dict Type Create API - Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Dict Type Create API - Error: $_" -ForegroundColor Red
}

Write-Host ""

# 测试现有API的兼容性
Write-Host "6. Testing Existing API Compatibility..." -ForegroundColor Yellow

# 用户列表
try {
    $userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers -TimeoutSec 10
    if ($userList.code -eq 200) {
        Write-Host "✅ User List API - Still Working" -ForegroundColor Green
    } else {
        Write-Host "❌ User List API - Broken" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ User List API - Error: $_" -ForegroundColor Red
}

# 角色列表
try {
    $roleList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/role/list" -Headers $headers -TimeoutSec 10
    if ($roleList.code -eq 200) {
        Write-Host "✅ Role List API - Still Working" -ForegroundColor Green
    } else {
        Write-Host "❌ Role List API - Broken" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Role List API - Error: $_" -ForegroundColor Red
}

# 菜单列表
try {
    $menuList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/menu/list" -Headers $headers -TimeoutSec 10
    if ($menuList.code -eq 200) {
        Write-Host "✅ Menu List API - Still Working" -ForegroundColor Green
    } else {
        Write-Host "❌ Menu List API - Broken" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Menu List API - Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Extended API Test Complete ===" -ForegroundColor Blue
