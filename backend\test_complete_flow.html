<!DOCTYPE html>
<html>
<head>
    <title>Complete Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 20px; }
    </style>
</head>
<body>
    <h1>Complete Login Flow Test</h1>
    
    <div>
        <button onclick="testCompleteFlow()">Test Complete Flow</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testCompleteFlow() {
            try {
                log('开始完整登录流程测试...', 'info');
                
                // 1. 获取验证码
                log('1. 获取验证码...', 'info');
                const captchaResponse = await fetch('/captchaImage');
                const captchaData = await captchaResponse.json();
                
                if (captchaData.code === 200) {
                    log('✅ 验证码获取成功', 'success');
                    log(`UUID: ${captchaData.uuid}`, 'info');
                } else {
                    log('❌ 验证码获取失败', 'error');
                    return;
                }

                // 2. 登录 (使用固定验证码答案，需要从日志中获取)
                log('2. 尝试登录...', 'info');
                const loginResponse = await fetch('/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        code: '2946', // 从日志中获取的验证码答案
                        uuid: captchaData.uuid
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (loginData.code === 200) {
                    log('✅ 登录成功', 'success');
                    log(`Token: ${loginData.data.token.substring(0, 20)}...`, 'info');
                    
                    const token = loginData.data.token;
                    
                    // 3. 获取用户信息
                    log('3. 获取用户信息...', 'info');
                    const userInfoResponse = await fetch('/getInfo', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    const userInfoData = await userInfoResponse.json();
                    
                    if (userInfoData.code === 200) {
                        log('✅ 用户信息获取成功', 'success');
                        log(`用户: ${userInfoData.data.user.userName}`, 'info');
                        log(`角色: ${userInfoData.data.roles.join(', ')}`, 'info');
                    } else {
                        log(`❌ 用户信息获取失败: ${userInfoData.msg}`, 'error');
                        return;
                    }
                    
                    // 4. 获取路由信息
                    log('4. 获取路由信息...', 'info');
                    const routersResponse = await fetch('/getRouters', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    const routersData = await routersResponse.json();
                    
                    if (routersData.code === 200) {
                        log('✅ 路由信息获取成功', 'success');
                        log(`路由数量: ${routersData.data.length}`, 'info');
                        log(`路由详情: ${JSON.stringify(routersData.data, null, 2)}`, 'info');
                    } else {
                        log(`❌ 路由信息获取失败: ${routersData.msg}`, 'error');
                        return;
                    }
                    
                    log('🎉 完整登录流程测试成功！', 'success');
                    
                } else {
                    log(`❌ 登录失败: ${loginData.msg}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
