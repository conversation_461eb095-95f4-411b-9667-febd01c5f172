package system

import (
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysDictTypeController 字典类型信息控制器
type SysDictTypeController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysDictTypeController 创建字典类型控制器
func NewSysDictTypeController(logger *zap.Logger, db *gorm.DB) *SysDictTypeController {
	return &SysDictTypeController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysDictTypeController的路由
func (c *SysDictTypeController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                    // 字典类型列表
	r.POST("/export", c.Export)               // 导出字典类型
	r.GET("/:dictId", c.GetInfo)              // 获取字典类型详情
	r.POST("", c.Add)                         // 新增字典类型
	r.PUT("", c.Edit)                         // 修改字典类型
	r.DELETE("/:dictIds", c.Remove)           // 删除字典类型
	r.DELETE("/refreshCache", c.RefreshCache) // 刷新字典缓存
	r.GET("/optionselect", c.Optionselect)    // 字典选择框
}

// List 获取字典类型列表 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var dictTypes []domain.SysDictType
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysDictType{})

	// 字典名称查询
	if dictName := ctx.Query("dictName"); dictName != "" {
		query = query.Where("dict_name LIKE ?", "%"+dictName+"%")
	}

	// 字典类型查询
	if dictType := ctx.Query("dictType"); dictType != "" {
		query = query.Where("dict_type LIKE ?", "%"+dictType+"%")
	}

	// 状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("dict_id ASC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&dictTypes).Error; err != nil {
		c.Logger.Error("查询字典类型列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  dictTypes,
	})
}

// GetInfo 根据字典ID获取详细信息 - 完全按照Java后端逻辑
func (c *SysDictTypeController) GetInfo(ctx *gin.Context) {
	dictIdStr := ctx.Param("dictId")
	dictId, err := strconv.ParseInt(dictIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "字典ID格式错误")
		return
	}

	var dictType domain.SysDictType
	if err := c.db.Where("dict_id = ?", dictId).First(&dictType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "字典类型不存在")
		} else {
			c.Logger.Error("查询字典类型信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, dictType)
}

// Add 新增字典类型 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) Add(ctx *gin.Context) {
	var dictType domain.SysDictType
	if err := ctx.ShouldBindJSON(&dictType); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if dictType.DictName == "" {
		c.ErrorWithMessage(ctx, "字典名称不能为空")
		return
	}
	if dictType.DictType == "" {
		c.ErrorWithMessage(ctx, "字典类型不能为空")
		return
	}
	if len(dictType.DictName) > 100 {
		c.ErrorWithMessage(ctx, "字典类型名称长度不能超过100个字符")
		return
	}
	if len(dictType.DictType) > 100 {
		c.ErrorWithMessage(ctx, "字典类型类型长度不能超过100个字符")
		return
	}

	// 字典类型格式验证 - 必须以字母开头，且只能为（小写字母，数字，下划线）
	matched, _ := regexp.MatchString("^[a-z][a-z0-9_]*$", dictType.DictType)
	if !matched {
		c.ErrorWithMessage(ctx, "字典类型必须以字母开头，且只能为（小写字母，数字，下划线）")
		return
	}

	// 检查字典类型唯一性
	if !c.checkDictTypeUnique(&dictType) {
		c.ErrorWithMessage(ctx, "新增字典'"+dictType.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置默认值
	dictType.CreateBy = c.GetUsername(ctx)
	if dictType.Status == "" {
		dictType.Status = "0" // 默认正常状态
	}

	// 保存字典类型
	if err := c.db.Create(&dictType).Error; err != nil {
		c.Logger.Error("新增字典类型失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改字典类型 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) Edit(ctx *gin.Context) {
	var dictType domain.SysDictType
	if err := ctx.ShouldBindJSON(&dictType); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if dictType.DictId == 0 {
		c.ErrorWithMessage(ctx, "字典ID不能为空")
		return
	}
	if dictType.DictName == "" {
		c.ErrorWithMessage(ctx, "字典名称不能为空")
		return
	}
	if dictType.DictType == "" {
		c.ErrorWithMessage(ctx, "字典类型不能为空")
		return
	}
	if len(dictType.DictName) > 100 {
		c.ErrorWithMessage(ctx, "字典类型名称长度不能超过100个字符")
		return
	}
	if len(dictType.DictType) > 100 {
		c.ErrorWithMessage(ctx, "字典类型类型长度不能超过100个字符")
		return
	}

	// 字典类型格式验证
	matched, _ := regexp.MatchString("^[a-z][a-z0-9_]*$", dictType.DictType)
	if !matched {
		c.ErrorWithMessage(ctx, "字典类型必须以字母开头，且只能为（小写字母，数字，下划线）")
		return
	}

	// 检查字典类型是否存在
	var existingDictType domain.SysDictType
	if err := c.db.Where("dict_id = ?", dictType.DictId).First(&existingDictType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "字典类型不存在")
		} else {
			c.Logger.Error("查询字典类型失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查字典类型唯一性（排除自己）
	if !c.checkDictTypeUnique(&dictType) {
		c.ErrorWithMessage(ctx, "修改字典'"+dictType.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置更新者
	dictType.UpdateBy = c.GetUsername(ctx)

	// 更新字典类型信息
	updateData := map[string]interface{}{
		"dict_name": dictType.DictName,
		"dict_type": dictType.DictType,
		"status":    dictType.Status,
		"update_by": dictType.UpdateBy,
		"remark":    dictType.Remark,
	}

	if err := c.db.Model(&existingDictType).Updates(updateData).Error; err != nil {
		c.Logger.Error("修改字典类型失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除字典类型 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) Remove(ctx *gin.Context) {
	dictIdsStr := ctx.Param("dictIds")
	dictIdStrs := strings.Split(dictIdsStr, ",")

	var dictIds []int64
	for _, dictIdStr := range dictIdStrs {
		dictId, err := strconv.ParseInt(dictIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "字典ID格式错误")
			return
		}
		dictIds = append(dictIds, dictId)
	}

	// 检查字典类型是否被字典数据使用
	for _, dictId := range dictIds {
		var dictType domain.SysDictType
		if err := c.db.Where("dict_id = ?", dictId).First(&dictType).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.ErrorWithMessage(ctx, "字典类型不存在")
			} else {
				c.Logger.Error("查询字典类型失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "查询失败")
			}
			return
		}

		var count int64
		if err := c.db.Model(&domain.SysDictData{}).Where("dict_type = ?", dictType.DictType).Count(&count).Error; err != nil {
			c.Logger.Error("查询字典数据使用情况失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
			return
		}
		if count > 0 {
			c.ErrorWithMessage(ctx, "字典类型已分配字典数据，不能删除")
			return
		}
	}

	// 删除字典类型
	if err := c.db.Where("dict_id IN ?", dictIds).Delete(&domain.SysDictType{}).Error; err != nil {
		c.Logger.Error("删除字典类型失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// RefreshCache 刷新字典缓存 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) RefreshCache(ctx *gin.Context) {
	// TODO: 实现字典缓存刷新逻辑
	c.Logger.Info("刷新字典缓存")
	c.SuccessWithMessage(ctx, "刷新缓存成功")
}

// Optionselect 获取字典选择框列表 - 完全按照Java后端业务逻辑
func (c *SysDictTypeController) Optionselect(ctx *gin.Context) {
	var dictTypes []domain.SysDictType

	// 查询所有正常状态的字典类型
	if err := c.db.Model(&domain.SysDictType{}).Where("status = ?", "0").Order("dict_id ASC").Find(&dictTypes).Error; err != nil {
		c.Logger.Error("查询字典类型选择框列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	c.SuccessWithData(ctx, dictTypes)
}

// Export 导出字典类型 - 简化实现
func (c *SysDictTypeController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 辅助函数

// checkDictTypeUnique 校验字典类型是否唯一
func (c *SysDictTypeController) checkDictTypeUnique(dictType *domain.SysDictType) bool {
	var count int64
	query := c.db.Model(&domain.SysDictType{}).Where("dict_type = ?", dictType.DictType)

	// 如果是修改操作，排除自己
	if dictType.DictId > 0 {
		query = query.Where("dict_id != ?", dictType.DictId)
	}

	query.Count(&count)
	return count == 0
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysDictTypeController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysDictTypeController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysDictTypeController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysDictTypeController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
