# WOSM Performance Test

param(
    [int]$Requests = 10,
    [int]$Concurrent = 2
)

Write-Host "=== WOSM Performance Test ===" -ForegroundColor Blue
Write-Host "Requests: $Requests" -ForegroundColor Yellow
Write-Host "Concurrent: $Concurrent" -ForegroundColor Yellow
Write-Host ""

$baseUrl = "http://localhost:8080"
$results = @()

# Login first to get token
Write-Host "Getting authentication token..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    $token = $loginResponse.data.token
    $headers = @{ Authorization = "Bearer $token" }
    Write-Host "✅ Token obtained" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get token: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test endpoints
$endpoints = @(
    @{ Name = "Health Check"; Url = "$baseUrl/health"; Method = "GET"; Headers = @{} },
    @{ Name = "User Info"; Url = "$baseUrl/api/getInfo"; Method = "GET"; Headers = $headers },
    @{ Name = "User List"; Url = "$baseUrl/api/system/user/list"; Method = "GET"; Headers = $headers },
    @{ Name = "Role List"; Url = "$baseUrl/api/system/role/list"; Method = "GET"; Headers = $headers }
)

foreach ($endpoint in $endpoints) {
    Write-Host "Testing $($endpoint.Name)..." -ForegroundColor Yellow
    
    $times = @()
    $errors = 0
    
    for ($i = 1; $i -le $Requests; $i++) {
        try {
            $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
            $response = Invoke-RestMethod -Uri $endpoint.Url -Method $endpoint.Method -Headers $endpoint.Headers -TimeoutSec 10
            $stopwatch.Stop()
            
            $times += $stopwatch.ElapsedMilliseconds
            Write-Host "." -NoNewline
        } catch {
            $errors++
            Write-Host "X" -NoNewline -ForegroundColor Red
        }
    }
    
    Write-Host ""
    
    if ($times.Count -gt 0) {
        $avgTime = [math]::Round(($times | Measure-Object -Average).Average, 2)
        $minTime = ($times | Measure-Object -Minimum).Minimum
        $maxTime = ($times | Measure-Object -Maximum).Maximum
        
        Write-Host "✅ $($endpoint.Name) Results:" -ForegroundColor Green
        Write-Host "   Average: $avgTime ms" -ForegroundColor Gray
        Write-Host "   Min: $minTime ms" -ForegroundColor Gray
        Write-Host "   Max: $maxTime ms" -ForegroundColor Gray
        Write-Host "   Errors: $errors" -ForegroundColor Gray
        
        $results += @{
            Endpoint = $endpoint.Name
            Average = $avgTime
            Min = $minTime
            Max = $maxTime
            Errors = $errors
            SuccessRate = [math]::Round((($Requests - $errors) / $Requests) * 100, 2)
        }
    } else {
        Write-Host "❌ All requests failed for $($endpoint.Name)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Summary
Write-Host "=== Performance Summary ===" -ForegroundColor Blue
foreach ($result in $results) {
    Write-Host "$($result.Endpoint):" -ForegroundColor Cyan
    Write-Host "  Avg: $($result.Average) ms | Success: $($result.SuccessRate)%" -ForegroundColor White
}

Write-Host ""

# System info
$process = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "=== System Resources ===" -ForegroundColor Blue
    Write-Host "Memory Usage: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor White
    Write-Host "CPU Time: $($process.CPU) seconds" -ForegroundColor White
    Write-Host "Threads: $($process.Threads.Count)" -ForegroundColor White
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Blue
