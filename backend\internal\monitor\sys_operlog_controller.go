package monitor

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysOperlogController 操作日志信息控制器
type SysOperlogController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysOperlogController 创建操作日志控制器
func NewSysOperlogController(logger *zap.Logger, db *gorm.DB) *SysOperlogController {
	return &SysOperlogController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysOperlogController的路由
func (c *SysOperlogController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)          // 操作日志列表
	r.POST("/export", c.Export)     // 导出操作日志
	r.DELETE("/:operIds", c.Remove) // 删除操作日志
	r.DELETE("/clean", c.Clean)     // 清空操作日志
}

// List 获取操作日志列表 - 完全按照Java后端业务逻辑
func (c *SysOperlogController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var operLogs []domain.SysOperLog
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysOperLog{})

	// 模块标题查询
	if title := ctx.Query("title"); title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	// 业务类型查询
	if businessType := ctx.Query("businessType"); businessType != "" {
		query = query.Where("business_type = ?", businessType)
	}

	// 业务类型数组查询
	if businessTypes := ctx.Query("businessTypes"); businessTypes != "" {
		businessTypeList := strings.Split(businessTypes, ",")
		query = query.Where("business_type IN ?", businessTypeList)
	}

	// 操作人员查询
	if operName := ctx.Query("operName"); operName != "" {
		query = query.Where("oper_name LIKE ?", "%"+operName+"%")
	}

	// 操作状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("oper_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("oper_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("oper_id DESC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&operLogs).Error; err != nil {
		c.Logger.Error("查询操作日志列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  operLogs,
	})
}

// Remove 删除操作日志 - 完全按照Java后端业务逻辑
func (c *SysOperlogController) Remove(ctx *gin.Context) {
	operIdsStr := ctx.Param("operIds")
	operIdStrs := strings.Split(operIdsStr, ",")

	var operIds []int64
	for _, operIdStr := range operIdStrs {
		operId, err := strconv.ParseInt(operIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "操作日志ID格式错误")
			return
		}
		operIds = append(operIds, operId)
	}

	// 删除操作日志
	if err := c.db.Where("oper_id IN ?", operIds).Delete(&domain.SysOperLog{}).Error; err != nil {
		c.Logger.Error("删除操作日志失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// Clean 清空操作日志 - 完全按照Java后端业务逻辑
func (c *SysOperlogController) Clean(ctx *gin.Context) {
	// 清空操作日志表
	if err := c.db.Exec("TRUNCATE TABLE sys_oper_log").Error; err != nil {
		c.Logger.Error("清空操作日志失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "清空失败")
		return
	}

	c.SuccessWithMessage(ctx, "清空成功")
}

// Export 导出操作日志 - 简化实现
func (c *SysOperlogController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysOperlogController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysOperlogController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysOperlogController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}
