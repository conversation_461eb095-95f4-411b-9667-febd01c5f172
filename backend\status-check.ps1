# WOSM Service Status Check

Write-Host "=== WOSM Service Status Check ===" -ForegroundColor Blue
Write-Host ""

# Check process
$process = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "✅ Service process is running" -ForegroundColor Green
    Write-Host "   PID: $($process.Id)" -ForegroundColor Gray
    Write-Host "   Memory: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor Gray
    Write-Host "   Start Time: $($process.StartTime)" -ForegroundColor Gray
} else {
    Write-Host "❌ Service process is not running" -ForegroundColor Red
}

Write-Host ""

# Check port
$port = 8080
$portInfo = netstat -ano | findstr ":$port "
if ($portInfo) {
    Write-Host "✅ Port $port is listening" -ForegroundColor Green
    $portInfo | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ Port $port is not listening" -ForegroundColor Red
}

Write-Host ""

# Health check
try {
    $health = Invoke-RestMethod -Uri "http://localhost:$port/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health check passed" -ForegroundColor Green
    Write-Host "   Service: $($health.service)" -ForegroundColor Gray
    Write-Host "   Version: $($health.version)" -ForegroundColor Gray
    Write-Host "   Status: $($health.status)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Health check failed" -ForegroundColor Red
    Write-Host "   Error: $_" -ForegroundColor Gray
}

Write-Host ""

# API test
try {
    Write-Host "🔍 Testing Login API..." -ForegroundColor Yellow
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:$port/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ Login API is working" -ForegroundColor Green
        
        # Test user list API
        $token = $loginResponse.data.token
        $headers = @{ Authorization = "Bearer $token" }
        $userList = Invoke-RestMethod -Uri "http://localhost:$port/api/system/user/list" -Headers $headers -TimeoutSec 10
        
        if ($userList.code -eq 200) {
            Write-Host "✅ User List API is working" -ForegroundColor Green
            Write-Host "   Total Users: $($userList.data.total)" -ForegroundColor Gray
        } else {
            Write-Host "❌ User List API failed" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Login API failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ API test failed" -ForegroundColor Red
    Write-Host "   Error: $_" -ForegroundColor Gray
}

Write-Host ""
Write-Host "=== Check Complete ===" -ForegroundColor Blue
