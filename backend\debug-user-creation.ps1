# Debug User Creation Issue

Write-Host "=== Debug User Creation Issue ===" -ForegroundColor Blue

# Get authentication token
$loginBody = '{"username":"admin","password":"admin123"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
$token = $loginResponse.data.token
$headers = @{ Authorization = "Bearer $token" }

Write-Host "Token obtained successfully" -ForegroundColor Green

# Test with minimal user data
Write-Host ""
Write-Host "1. Testing with minimal user data..." -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    $minimalUserBody = @{
        userName = "testuser$timestamp"
        password = "123456"
        status = "0"
    } | ConvertTo-Json
    
    Write-Host "Minimal User Body: $minimalUserBody" -ForegroundColor Gray
    
    $minimalResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $minimalUserBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Minimal User Response: $($minimalResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Minimal User Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Test with standard user data
Write-Host ""
Write-Host "2. Testing with standard user data..." -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    $standardUserBody = @{
        userName = "testuser$timestamp"
        nickName = "Test User $timestamp"
        email = "testuser$<EMAIL>"
        phonenumber = "139$timestamp"
        sex = "0"
        password = "123456"
        status = "0"
        deptId = 100
        remark = "Test user for integration testing"
    } | ConvertTo-Json
    
    Write-Host "Standard User Body: $standardUserBody" -ForegroundColor Gray
    
    $standardResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $standardUserBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Standard User Response: $($standardResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Standard User Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Test with user data without roles and posts
Write-Host ""
Write-Host "3. Testing without roles and posts..." -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    $noRoleUserBody = @{
        userName = "testuser$timestamp"
        nickName = "Test User $timestamp"
        email = "testuser$<EMAIL>"
        phonenumber = "139$timestamp"
        sex = "0"
        password = "123456"
        status = "0"
        deptId = 100
        userType = "00"
        remark = "Test user for integration testing"
    } | ConvertTo-Json
    
    Write-Host "No Role User Body: $noRoleUserBody" -ForegroundColor Gray
    
    $noRoleResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $noRoleUserBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "No Role User Response: $($noRoleResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "No Role User Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug completed!" -ForegroundColor Blue
