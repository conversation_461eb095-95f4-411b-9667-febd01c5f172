package domain

import (
	"time"
)

// SysUser 用户对象 sys_user
type SysUser struct {
	BaseEntity

	// 用户ID
	UserId int64 `json:"userId" gorm:"column:user_id;primary_key"`

	// 部门ID
	DeptId int64 `json:"deptId" gorm:"column:dept_id"`

	// 登录账号
	LoginName string `json:"loginName" gorm:"column:login_name"`

	// 用户账号
	UserName string `json:"userName" gorm:"column:user_name"`

	// 用户类型
	UserType string `json:"userType" gorm:"column:user_type"`

	// 用户昵称 (数据库中不存在此字段，忽略)
	NickName string `json:"nickName" gorm:"-"`

	// 用户邮箱
	Email string `json:"email" gorm:"column:email"`

	// 手机号码
	Phonenumber string `json:"phonenumber" gorm:"column:phonenumber"`

	// 用户性别
	Sex string `json:"sex" gorm:"column:sex"`

	// 用户头像
	Avatar string `json:"avatar" gorm:"column:avatar"`

	// 密码
	Password string `json:"password" gorm:"column:password"`

	// 盐加密
	Salt string `json:"salt" gorm:"column:salt"`

	// 帐号状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag"`

	// 最后登录IP
	LoginIp string `json:"loginIp" gorm:"column:login_ip"`

	// 最后登录时间
	LoginDate *time.Time `json:"loginDate" gorm:"column:login_date"`

	// 部门对象
	Dept *SysDept `json:"dept" gorm:"-"`

	// 角色对象
	Roles []SysRole `json:"roles" gorm:"-"`

	// 角色组
	RoleIds []int64 `json:"roleIds" gorm:"-"`

	// 岗位组
	PostIds []int64 `json:"postIds" gorm:"-"`

	// 数据权限 当前角色ID
	RoleId int64 `json:"roleId" gorm:"-"`

	// 数据范围（1：全部数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
	DataScope string `json:"dataScope" gorm:"-"`
}

// TableName 设置表名
func (SysUser) TableName() string {
	return "sys_user"
}

// GetUserId 获取用户ID
func (u *SysUser) GetUserId() int64 {
	return u.UserId
}

// SetUserId 设置用户ID
func (u *SysUser) SetUserId(userId int64) {
	u.UserId = userId
}

// GetDeptId 获取部门ID
func (u *SysUser) GetDeptId() int64 {
	return u.DeptId
}

// SetDeptId 设置部门ID
func (u *SysUser) SetDeptId(deptId int64) {
	u.DeptId = deptId
}

// GetUserName 获取用户账号
func (u *SysUser) GetUserName() string {
	return u.UserName
}

// SetUserName 设置用户账号
func (u *SysUser) SetUserName(userName string) {
	u.UserName = userName
}

// GetNickName 获取用户昵称
func (u *SysUser) GetNickName() string {
	return u.NickName
}

// SetNickName 设置用户昵称
func (u *SysUser) SetNickName(nickName string) {
	u.NickName = nickName
}

// GetEmail 获取用户邮箱
func (u *SysUser) GetEmail() string {
	return u.Email
}

// SetEmail 设置用户邮箱
func (u *SysUser) SetEmail(email string) {
	u.Email = email
}

// GetPhonenumber 获取手机号码
func (u *SysUser) GetPhonenumber() string {
	return u.Phonenumber
}

// SetPhonenumber 设置手机号码
func (u *SysUser) SetPhonenumber(phonenumber string) {
	u.Phonenumber = phonenumber
}

// GetSex 获取用户性别
func (u *SysUser) GetSex() string {
	return u.Sex
}

// SetSex 设置用户性别
func (u *SysUser) SetSex(sex string) {
	u.Sex = sex
}

// GetAvatar 获取用户头像
func (u *SysUser) GetAvatar() string {
	return u.Avatar
}

// SetAvatar 设置用户头像
func (u *SysUser) SetAvatar(avatar string) {
	u.Avatar = avatar
}

// GetPassword 获取密码
func (u *SysUser) GetPassword() string {
	return u.Password
}

// SetPassword 设置密码
func (u *SysUser) SetPassword(password string) {
	u.Password = password
}

// GetSalt 获取盐加密
func (u *SysUser) GetSalt() string {
	return u.Salt
}

// SetSalt 设置盐加密
func (u *SysUser) SetSalt(salt string) {
	u.Salt = salt
}

// GetStatus 获取帐号状态
func (u *SysUser) GetStatus() string {
	return u.Status
}

// SetStatus 设置帐号状态
func (u *SysUser) SetStatus(status string) {
	u.Status = status
}

// GetDelFlag 获取删除标志
func (u *SysUser) GetDelFlag() string {
	return u.DelFlag
}

// SetDelFlag 设置删除标志
func (u *SysUser) SetDelFlag(delFlag string) {
	u.DelFlag = delFlag
}

// GetLoginIp 获取最后登录IP
func (u *SysUser) GetLoginIp() string {
	return u.LoginIp
}

// SetLoginIp 设置最后登录IP
func (u *SysUser) SetLoginIp(loginIp string) {
	u.LoginIp = loginIp
}

// GetLoginDate 获取最后登录时间
func (u *SysUser) GetLoginDate() *time.Time {
	return u.LoginDate
}

// SetLoginDate 设置最后登录时间
func (u *SysUser) SetLoginDate(loginDate *time.Time) {
	u.LoginDate = loginDate
}

// GetDept 获取部门对象
func (u *SysUser) GetDept() *SysDept {
	return u.Dept
}

// SetDept 设置部门对象
func (u *SysUser) SetDept(dept *SysDept) {
	u.Dept = dept
}

// GetRoles 获取角色对象
func (u *SysUser) GetRoles() []SysRole {
	return u.Roles
}

// SetRoles 设置角色对象
func (u *SysUser) SetRoles(roles []SysRole) {
	u.Roles = roles
}

// GetRoleIds 获取角色组
func (u *SysUser) GetRoleIds() []int64 {
	return u.RoleIds
}

// SetRoleIds 设置角色组
func (u *SysUser) SetRoleIds(roleIds []int64) {
	u.RoleIds = roleIds
}

// GetPostIds 获取岗位组
func (u *SysUser) GetPostIds() []int64 {
	return u.PostIds
}

// SetPostIds 设置岗位组
func (u *SysUser) SetPostIds(postIds []int64) {
	u.PostIds = postIds
}

// GetRoleId 获取数据权限
func (u *SysUser) GetRoleId() int64 {
	return u.RoleId
}

// SetRoleId 设置数据权限
func (u *SysUser) SetRoleId(roleId int64) {
	u.RoleId = roleId
}

// GetDataScope 获取数据范围
func (u *SysUser) GetDataScope() string {
	return u.DataScope
}

// SetDataScope 设置数据范围
func (u *SysUser) SetDataScope(dataScope string) {
	u.DataScope = dataScope
}
