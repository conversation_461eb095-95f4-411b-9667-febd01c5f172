package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// X-Frame-Options: 防止点击劫持攻击
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")

		// X-Content-Type-Options: 防止MIME类型嗅探
		c.Head<PERSON>("X-Content-Type-Options", "nosniff")

		// X-XSS-Protection: 启用XSS过滤器
		c.Header("X-XSS-Protection", "1; mode=block")

		// Strict-Transport-Security: 强制HTTPS
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")

		// Referrer-Policy: 控制Referer头的发送
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		// Content-Security-Policy: 内容安全策略
		csp := "default-src 'self'; " +
			"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; " +
			"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " +
			"font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; " +
			"img-src 'self' data: https: blob:; " +
			"connect-src 'self' https: wss:; " +
			"media-src 'self'; " +
			"object-src 'none'; " +
			"frame-ancestors 'none'; " +
			"base-uri 'self'; " +
			"form-action 'self';"
		c.Header("Content-Security-Policy", csp)

		// Permissions-Policy: 权限策略
		permissionsPolicy := "camera=(), " +
			"microphone=(), " +
			"geolocation=(), " +
			"payment=(), " +
			"usb=(), " +
			"magnetometer=(), " +
			"accelerometer=(), " +
			"gyroscope=(), " +
			"fullscreen=(self)"
		c.Header("Permissions-Policy", permissionsPolicy)

		// X-Permitted-Cross-Domain-Policies: 限制跨域策略文件
		c.Header("X-Permitted-Cross-Domain-Policies", "none")

		// Cache-Control: 缓存控制
		if c.Request.URL.Path == "/api/login" ||
			c.Request.URL.Path == "/api/getInfo" ||
			c.Request.URL.Path == "/api/captchaImage" {
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		c.Next()
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 允许的域名列表
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8080",
			// 生产环境需要配置实际的域名
		}

		// 检查Origin是否在允许列表中
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RequestSizeMiddleware 请求大小限制中间件
func RequestSizeMiddleware(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(413, gin.H{
				"code": 413,
				"msg":  "请求体过大",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// IPWhitelistMiddleware IP白名单中间件
func IPWhitelistMiddleware(whitelist []string) gin.HandlerFunc {
	whitelistMap := make(map[string]bool)
	for _, ip := range whitelist {
		whitelistMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		// 如果白名单为空，则允许所有IP
		if len(whitelistMap) == 0 {
			c.Next()
			return
		}

		if !whitelistMap[clientIP] {
			c.JSON(403, gin.H{
				"code": 403,
				"msg":  "访问被拒绝",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// IPBlacklistMiddleware IP黑名单中间件
func IPBlacklistMiddleware(blacklist []string) gin.HandlerFunc {
	blacklistMap := make(map[string]bool)
	for _, ip := range blacklist {
		blacklistMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if blacklistMap[clientIP] {
			c.JSON(403, gin.H{
				"code": 403,
				"msg":  "访问被拒绝",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// UserAgentFilterMiddleware User-Agent过滤中间件
func UserAgentFilterMiddleware() gin.HandlerFunc {
	// 可疑的User-Agent模式
	suspiciousPatterns := []string{
		"sqlmap",
		"nmap",
		"nikto",
		"w3af",
		"acunetix",
		"netsparker",
		"burpsuite",
		"owasp",
		"scanner",
		"crawler",
		"bot",
		"spider",
		"scraper",
	}

	return func(c *gin.Context) {
		userAgent := c.Request.UserAgent()

		// 检查是否为空或过短
		if len(userAgent) < 10 {
			c.JSON(400, gin.H{
				"code": 400,
				"msg":  "无效的请求",
			})
			c.Abort()
			return
		}

		// 检查可疑模式
		userAgentLower := strings.ToLower(userAgent)
		for _, pattern := range suspiciousPatterns {
			if strings.Contains(userAgentLower, pattern) {
				c.JSON(403, gin.H{
					"code": 403,
					"msg":  "访问被拒绝",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}
