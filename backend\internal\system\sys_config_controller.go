package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysConfigController 参数配置信息控制器
type SysConfigController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysConfigController 创建参数配置控制器
func NewSysConfigController(logger *zap.Logger, db *gorm.DB) *SysConfigController {
	return &SysConfigController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysConfigController的路由
func (c *SysConfigController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                         // 参数配置列表
	r.POST("/export", c.Export)                    // 导出参数配置
	r.GET("/:configId", c.GetInfo)                 // 获取参数配置详情
	r.GET("/configKey/:configKey", c.GetConfigKey) // 根据参数键名查询参数值
	r.POST("", c.Add)                              // 新增参数配置
	r.PUT("", c.Edit)                              // 修改参数配置
	r.DELETE("/:configIds", c.Remove)              // 删除参数配置
	r.DELETE("/refreshCache", c.RefreshCache)      // 刷新参数缓存
}

// List 获取参数配置列表 - 完全按照Java后端业务逻辑
func (c *SysConfigController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var configs []domain.SysConfig
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysConfig{})

	// 参数名称查询
	if configName := ctx.Query("configName"); configName != "" {
		query = query.Where("config_name LIKE ?", "%"+configName+"%")
	}

	// 参数键名查询
	if configKey := ctx.Query("configKey"); configKey != "" {
		query = query.Where("config_key LIKE ?", "%"+configKey+"%")
	}

	// 系统内置查询
	if configType := ctx.Query("configType"); configType != "" {
		query = query.Where("config_type = ?", configType)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("config_id ASC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&configs).Error; err != nil {
		c.Logger.Error("查询参数配置列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  configs,
	})
}

// GetInfo 根据参数ID获取详细信息 - 完全按照Java后端逻辑
func (c *SysConfigController) GetInfo(ctx *gin.Context) {
	configIdStr := ctx.Param("configId")
	configId, err := strconv.ParseInt(configIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "参数ID格式错误")
		return
	}

	var config domain.SysConfig
	if err := c.db.Where("config_id = ?", configId).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "参数配置不存在")
		} else {
			c.Logger.Error("查询参数配置信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, config)
}

// GetConfigKey 根据参数键名查询参数值 - 完全按照Java后端逻辑
func (c *SysConfigController) GetConfigKey(ctx *gin.Context) {
	configKey := ctx.Param("configKey")

	var config domain.SysConfig
	if err := c.db.Where("config_key = ?", configKey).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "参数键名不存在")
		} else {
			c.Logger.Error("根据参数键名查询参数值失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, config.ConfigValue)
}

// Add 新增参数配置 - 完全按照Java后端业务逻辑
func (c *SysConfigController) Add(ctx *gin.Context) {
	var config domain.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if config.ConfigName == "" {
		c.ErrorWithMessage(ctx, "参数名称不能为空")
		return
	}
	if config.ConfigKey == "" {
		c.ErrorWithMessage(ctx, "参数键名不能为空")
		return
	}
	if config.ConfigValue == "" {
		c.ErrorWithMessage(ctx, "参数键值不能为空")
		return
	}
	if len(config.ConfigName) > 100 {
		c.ErrorWithMessage(ctx, "参数名称不能超过100个字符")
		return
	}
	if len(config.ConfigKey) > 100 {
		c.ErrorWithMessage(ctx, "参数键名长度不能超过100个字符")
		return
	}
	if len(config.ConfigValue) > 500 {
		c.ErrorWithMessage(ctx, "参数键值长度不能超过500个字符")
		return
	}

	// 检查参数键名唯一性
	if !c.checkConfigKeyUnique(&config) {
		c.ErrorWithMessage(ctx, "新增参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置默认值
	config.CreateBy = c.GetUsername(ctx)
	if config.ConfigType == "" {
		config.ConfigType = "N" // 默认非系统内置
	}

	// 保存参数配置
	if err := c.db.Create(&config).Error; err != nil {
		c.Logger.Error("新增参数配置失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改参数配置 - 完全按照Java后端业务逻辑
func (c *SysConfigController) Edit(ctx *gin.Context) {
	var config domain.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if config.ConfigId == 0 {
		c.ErrorWithMessage(ctx, "参数ID不能为空")
		return
	}
	if config.ConfigName == "" {
		c.ErrorWithMessage(ctx, "参数名称不能为空")
		return
	}
	if config.ConfigKey == "" {
		c.ErrorWithMessage(ctx, "参数键名不能为空")
		return
	}
	if config.ConfigValue == "" {
		c.ErrorWithMessage(ctx, "参数键值不能为空")
		return
	}
	if len(config.ConfigName) > 100 {
		c.ErrorWithMessage(ctx, "参数名称不能超过100个字符")
		return
	}
	if len(config.ConfigKey) > 100 {
		c.ErrorWithMessage(ctx, "参数键名长度不能超过100个字符")
		return
	}
	if len(config.ConfigValue) > 500 {
		c.ErrorWithMessage(ctx, "参数键值长度不能超过500个字符")
		return
	}

	// 检查参数配置是否存在
	var existingConfig domain.SysConfig
	if err := c.db.Where("config_id = ?", config.ConfigId).First(&existingConfig).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "参数配置不存在")
		} else {
			c.Logger.Error("查询参数配置失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查参数键名唯一性（排除自己）
	if !c.checkConfigKeyUnique(&config) {
		c.ErrorWithMessage(ctx, "修改参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置更新者
	config.UpdateBy = c.GetUsername(ctx)

	// 更新参数配置信息
	updateData := map[string]interface{}{
		"config_name":  config.ConfigName,
		"config_key":   config.ConfigKey,
		"config_value": config.ConfigValue,
		"config_type":  config.ConfigType,
		"update_by":    config.UpdateBy,
		"remark":       config.Remark,
	}

	if err := c.db.Model(&existingConfig).Updates(updateData).Error; err != nil {
		c.Logger.Error("修改参数配置失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除参数配置 - 完全按照Java后端业务逻辑
func (c *SysConfigController) Remove(ctx *gin.Context) {
	configIdsStr := ctx.Param("configIds")
	configIdStrs := strings.Split(configIdsStr, ",")

	var configIds []int64
	for _, configIdStr := range configIdStrs {
		configId, err := strconv.ParseInt(configIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "参数ID格式错误")
			return
		}
		configIds = append(configIds, configId)
	}

	// 检查是否包含系统内置参数
	for _, configId := range configIds {
		var config domain.SysConfig
		if err := c.db.Where("config_id = ?", configId).First(&config).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.ErrorWithMessage(ctx, "参数配置不存在")
			} else {
				c.Logger.Error("查询参数配置失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "查询失败")
			}
			return
		}

		if config.ConfigType == "Y" {
			c.ErrorWithMessage(ctx, "内置参数【"+config.ConfigKey+"】不能删除")
			return
		}
	}

	// 删除参数配置
	if err := c.db.Where("config_id IN ?", configIds).Delete(&domain.SysConfig{}).Error; err != nil {
		c.Logger.Error("删除参数配置失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// RefreshCache 刷新参数缓存 - 完全按照Java后端业务逻辑
func (c *SysConfigController) RefreshCache(ctx *gin.Context) {
	// TODO: 实现参数配置缓存刷新逻辑
	c.Logger.Info("刷新参数配置缓存")
	c.SuccessWithMessage(ctx, "刷新缓存成功")
}

// Export 导出参数配置 - 简化实现
func (c *SysConfigController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 辅助函数

// checkConfigKeyUnique 校验参数键名是否唯一
func (c *SysConfigController) checkConfigKeyUnique(config *domain.SysConfig) bool {
	var count int64
	query := c.db.Model(&domain.SysConfig{}).Where("config_key = ?", config.ConfigKey)

	// 如果是修改操作，排除自己
	if config.ConfigId > 0 {
		query = query.Where("config_id != ?", config.ConfigId)
	}

	query.Count(&count)
	return count == 0
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysConfigController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysConfigController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysConfigController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysConfigController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
