package middleware

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// TokenInfo JWT令牌信息
type TokenInfo struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		token := c.GetHeader("Authorization")
		if token == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "未提供认证令牌",
			})
			return
		}

		// 移除Bearer前缀
		if strings.HasPrefix(token, "Bearer ") {
			token = token[7:]
		}

		// 验证JWT令牌
		claims, err := ParseToken(token)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "无效的认证令牌: " + err.Error(),
			})
			return
		}

		// 检查令牌是否过期
		if time.Now().Unix() > claims.ExpiresAt.Unix() {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证令牌已过期",
			})
			return
		}

		// 将用户信息存储到上下文
		c.Set("X-User-Id", claims.UserID)
		c.Set("X-Username", claims.Username)
		c.Next()
	}
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*TokenInfo, error) {
	// 获取JWT密钥
	jwtSecret := config.GetJWTSecret()
	if jwtSecret == "" {
		return nil, errors.New("未配置JWT密钥")
	}

	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &TokenInfo{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, err
	}

	// 验证令牌类型
	if claims, ok := token.Claims.(*TokenInfo); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID int64, username string) (string, error) {
	// 获取JWT配置
	jwtSecret := config.GetJWTSecret()
	if jwtSecret == "" {
		return "", errors.New("未配置JWT密钥")
	}
	jwtExpire := config.GetJWTExpire()

	// 创建JWT声明
	claims := TokenInfo{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(jwtExpire) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "ruoyi-go",
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名令牌
	return token.SignedString([]byte(jwtSecret))
}

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(logger *zap.Logger, tokenManager *auth.TokenManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求头中的token
		token := c.GetHeader(auth.TokenHeader)

		// 如果请求头中没有token，则从查询参数中获取
		if token == "" {
			token = c.Query("token")
		}

		// 如果没有token，则返回未授权错误
		if token == "" {
			result := model.AjaxResult{
				Code: http.StatusUnauthorized,
				Msg:  "请求未授权，无法访问系统资源",
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, result)
			return
		}

		// 如果token不是以Bearer开头，则添加前缀
		if !strings.HasPrefix(token, auth.TokenPrefix) {
			token = auth.TokenPrefix + token
		}

		// 获取登录用户
		loginUser := tokenManager.GetLoginUser(c)
		if loginUser == nil {
			result := model.AjaxResult{
				Code: http.StatusUnauthorized,
				Msg:  "登录状态已过期，请重新登录",
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, result)
			return
		}

		// 验证token有效期，如果快过期则自动刷新
		tokenManager.VerifyToken(loginUser)

		// 将用户信息保存到上下文中
		c.Set("loginUser", loginUser)

		// 继续处理请求
		c.Next()
	}
}

// GetLoginUser 从上下文中获取登录用户
func GetLoginUser(c *gin.Context) *model.LoginUser {
	value, exists := c.Get("loginUser")
	if !exists {
		return nil
	}

	if loginUser, ok := value.(*model.LoginUser); ok {
		return loginUser
	}

	return nil
}
