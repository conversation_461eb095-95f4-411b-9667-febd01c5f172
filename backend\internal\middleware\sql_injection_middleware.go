package middleware

import (
	"io"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SQLInjectionMiddleware SQL注入防护中间件
func SQLInjectionMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// SQL注入关键词检测
		sqlKeywords := []string{
			"select", "insert", "update", "delete", "drop", "create", "alter",
			"union", "exec", "execute", "script", "declare", "cast", "convert",
			"having", "where", "order", "group", "by", "and", "or", "not",
			"xp_", "sp_", "sys", "information_schema", "master", "msdb",
			"--", "/*", "*/", "@@", "char", "nchar", "varchar", "nvarchar",
			"waitfor", "delay", "benchmark", "sleep", "pg_sleep",
		}

		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsSQLInjection(value, sqlKeywords) {
					logger.Warn("检测到SQL注入攻击",
						zap.String("ip", c.ClientIP()),
						zap.String("url", c.Request.URL.String()),
						zap.String("param", key),
						zap.String("value", value),
						zap.String("userAgent", c.Request.UserAgent()))

					c.JSON(http.StatusBadRequest, gin.H{
						"code": 400,
						"msg":  "请求参数包含非法字符",
					})
					c.Abort()
					return
				}
			}
		}

		// 检查POST数据（如果是JSON）
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			contentType := c.GetHeader("Content-Type")
			if strings.Contains(contentType, "application/json") {
				// 读取请求体
				body, err := c.GetRawData()
				if err == nil {
					bodyStr := string(body)
					if containsSQLInjection(bodyStr, sqlKeywords) {
						logger.Warn("检测到SQL注入攻击",
							zap.String("ip", c.ClientIP()),
							zap.String("url", c.Request.URL.String()),
							zap.String("method", c.Request.Method),
							zap.String("body", bodyStr),
							zap.String("userAgent", c.Request.UserAgent()))

						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "请求数据包含非法字符",
						})
						c.Abort()
						return
					}
					// 重新设置请求体
					c.Request.Body = io.NopCloser(strings.NewReader(bodyStr))
				}
			}
		}

		c.Next()
	}
}

// containsSQLInjection 检查字符串是否包含SQL注入
func containsSQLInjection(input string, keywords []string) bool {
	input = strings.ToLower(input)

	// 跳过正常的JSON字段
	if strings.Contains(input, `"username"`) || strings.Contains(input, `"password"`) {
		return false
	}

	// 检查SQL关键词
	for _, keyword := range keywords {
		if strings.Contains(input, keyword) {
			return true
		}
	}

	// 检查常见SQL注入模式
	patterns := []string{
		`\b(union\s+select)\b`,
		`\b(select\s+.*\s+from)\b`,
		`\b(insert\s+into)\b`,
		`\b(update\s+.*\s+set)\b`,
		`\b(delete\s+from)\b`,
		`\b(drop\s+table)\b`,
		`\b(create\s+table)\b`,
		`\b(alter\s+table)\b`,
		`\b(exec\s*\()\b`,
		`\b(execute\s*\()\b`,
		`\b(waitfor\s+delay)\b`,
		`\b(benchmark\s*\()\b`,
		`\b(sleep\s*\()\b`,
		`\b(pg_sleep\s*\()\b`,
		`(\-\-|\#|\/\*|\*\/)`,
		`(\bor\b|\band\b)\s+\d+\s*=\s*\d+`,
		`(\bor\b|\band\b)\s+\w+\s*=\s*\w+`,
		`\'\s*(or|and)\s+\'\w*\'\s*=\s*\'\w*\'`,
		`\'\s*(or|and)\s+\d+\s*=\s*\d+`,
		`\'\s*;\s*(select|insert|update|delete|drop|create|alter)`,
		`\bunion\s+all\s+select\b`,
		`\bconcat\s*\(`,
		`\bchar\s*\(\d+\)`,
		`\bascii\s*\(`,
		`\bsubstring\s*\(`,
		`\blength\s*\(`,
		`\bcount\s*\(.*\*.*\)`,
		`\bgroup_concat\s*\(`,
		`\bload_file\s*\(`,
		`\binto\s+outfile\b`,
		`\binto\s+dumpfile\b`,
		`\bxp_cmdshell\b`,
		`\bsp_executesql\b`,
		`\bopenrowset\b`,
		`\bopendatasource\b`,
	}

	for _, pattern := range patterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}

	return false
}

// XSSProtectionMiddleware XSS防护中间件
func XSSProtectionMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// XSS攻击模式
		xssPatterns := []string{
			`<script[^>]*>.*?</script>`,
			`<iframe[^>]*>.*?</iframe>`,
			`<object[^>]*>.*?</object>`,
			`<embed[^>]*>.*?</embed>`,
			`<applet[^>]*>.*?</applet>`,
			`<meta[^>]*>`,
			`<link[^>]*>`,
			`<style[^>]*>.*?</style>`,
			`javascript:`,
			`vbscript:`,
			`onload\s*=`,
			`onerror\s*=`,
			`onclick\s*=`,
			`onmouseover\s*=`,
			`onfocus\s*=`,
			`onblur\s*=`,
			`onchange\s*=`,
			`onsubmit\s*=`,
			`alert\s*\(`,
			`confirm\s*\(`,
			`prompt\s*\(`,
			`document\.cookie`,
			`document\.write`,
			`window\.location`,
			`eval\s*\(`,
			`expression\s*\(`,
			`<img[^>]*src\s*=\s*["\']?javascript:`,
			`<a[^>]*href\s*=\s*["\']?javascript:`,
		}

		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsXSS(value, xssPatterns) {
					logger.Warn("检测到XSS攻击",
						zap.String("ip", c.ClientIP()),
						zap.String("url", c.Request.URL.String()),
						zap.String("param", key),
						zap.String("value", value),
						zap.String("userAgent", c.Request.UserAgent()))

					c.JSON(http.StatusBadRequest, gin.H{
						"code": 400,
						"msg":  "请求参数包含非法脚本",
					})
					c.Abort()
					return
				}
			}
		}

		// 检查POST数据
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			contentType := c.GetHeader("Content-Type")
			if strings.Contains(contentType, "application/json") {
				body, err := c.GetRawData()
				if err == nil {
					bodyStr := string(body)
					if containsXSS(bodyStr, xssPatterns) {
						logger.Warn("检测到XSS攻击",
							zap.String("ip", c.ClientIP()),
							zap.String("url", c.Request.URL.String()),
							zap.String("method", c.Request.Method),
							zap.String("body", bodyStr),
							zap.String("userAgent", c.Request.UserAgent()))

						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "请求数据包含非法脚本",
						})
						c.Abort()
						return
					}
					c.Request.Body = io.NopCloser(strings.NewReader(bodyStr))
				}
			}
		}

		// 设置XSS防护响应头
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'; frame-ancestors 'none';")

		c.Next()
	}
}

// containsXSS 检查字符串是否包含XSS攻击
func containsXSS(input string, patterns []string) bool {
	input = strings.ToLower(input)

	for _, pattern := range patterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}

	return false
}

// CSRFProtectionMiddleware CSRF防护中间件
func CSRFProtectionMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 对于状态改变的请求（POST, PUT, DELETE）进行CSRF检查
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "DELETE" {
			// 检查Referer头
			referer := c.GetHeader("Referer")
			origin := c.GetHeader("Origin")
			host := c.GetHeader("Host")

			// 如果有Origin头，优先使用Origin
			if origin != "" {
				if !strings.Contains(origin, host) {
					logger.Warn("检测到CSRF攻击",
						zap.String("ip", c.ClientIP()),
						zap.String("url", c.Request.URL.String()),
						zap.String("method", c.Request.Method),
						zap.String("origin", origin),
						zap.String("host", host),
						zap.String("userAgent", c.Request.UserAgent()))

					c.JSON(http.StatusForbidden, gin.H{
						"code": 403,
						"msg":  "跨站请求伪造攻击",
					})
					c.Abort()
					return
				}
			} else if referer != "" {
				// 如果没有Origin头，检查Referer
				if !strings.Contains(referer, host) {
					logger.Warn("检测到CSRF攻击",
						zap.String("ip", c.ClientIP()),
						zap.String("url", c.Request.URL.String()),
						zap.String("method", c.Request.Method),
						zap.String("referer", referer),
						zap.String("host", host),
						zap.String("userAgent", c.Request.UserAgent()))

					c.JSON(http.StatusForbidden, gin.H{
						"code": 403,
						"msg":  "跨站请求伪造攻击",
					})
					c.Abort()
					return
				}
			}
		}

		c.Next()
	}
}
