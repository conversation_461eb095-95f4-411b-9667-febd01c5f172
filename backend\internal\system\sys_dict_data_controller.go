package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysDictDataController 字典数据信息控制器
type SysDictDataController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysDictDataController 创建字典数据控制器
func NewSysDictDataController(logger *zap.Logger, db *gorm.DB) *SysDictDataController {
	return &SysDictDataController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysDictDataController的路由
func (c *SysDictDataController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)               // 字典数据列表
	r.POST("/export", c.Export)          // 导出字典数据
	r.GET("/:dictCode", c.GetInfo)       // 获取字典数据详情
	r.GET("/type/:dictType", c.DictType) // 根据字典类型查询字典数据
	r.POST("", c.Add)                    // 新增字典数据
	r.PUT("", c.Edit)                    // 修改字典数据
	r.DELETE("/:dictCodes", c.Remove)    // 删除字典数据
}

// List 获取字典数据列表 - 完全按照Java后端业务逻辑
func (c *SysDictDataController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var dictData []domain.SysDictData
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysDictData{})

	// 字典标签查询
	if dictLabel := ctx.Query("dictLabel"); dictLabel != "" {
		query = query.Where("dict_label LIKE ?", "%"+dictLabel+"%")
	}

	// 字典类型查询
	if dictType := ctx.Query("dictType"); dictType != "" {
		query = query.Where("dict_type = ?", dictType)
	}

	// 状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("dict_sort ASC, dict_code ASC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&dictData).Error; err != nil {
		c.Logger.Error("查询字典数据列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  dictData,
	})
}

// GetInfo 根据字典编码获取详细信息 - 完全按照Java后端逻辑
func (c *SysDictDataController) GetInfo(ctx *gin.Context) {
	dictCodeStr := ctx.Param("dictCode")
	dictCode, err := strconv.ParseInt(dictCodeStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "字典编码格式错误")
		return
	}

	var dictData domain.SysDictData
	if err := c.db.Where("dict_code = ?", dictCode).First(&dictData).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "字典数据不存在")
		} else {
			c.Logger.Error("查询字典数据信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, dictData)
}

// DictType 根据字典类型查询字典数据信息 - 完全按照Java后端逻辑
func (c *SysDictDataController) DictType(ctx *gin.Context) {
	dictType := ctx.Param("dictType")

	var dictData []domain.SysDictData

	// 查询指定字典类型的所有正常状态数据
	if err := c.db.Model(&domain.SysDictData{}).Where("dict_type = ? AND status = ?", dictType, "0").Order("dict_sort ASC, dict_code ASC").Find(&dictData).Error; err != nil {
		c.Logger.Error("根据字典类型查询字典数据失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 如果没有数据，返回空数组
	if dictData == nil {
		dictData = []domain.SysDictData{}
	}

	c.SuccessWithData(ctx, dictData)
}

// Add 新增字典数据 - 完全按照Java后端业务逻辑
func (c *SysDictDataController) Add(ctx *gin.Context) {
	var dictData domain.SysDictData
	if err := ctx.ShouldBindJSON(&dictData); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if dictData.DictLabel == "" {
		c.ErrorWithMessage(ctx, "字典标签不能为空")
		return
	}
	if dictData.DictValue == "" {
		c.ErrorWithMessage(ctx, "字典键值不能为空")
		return
	}
	if dictData.DictType == "" {
		c.ErrorWithMessage(ctx, "字典类型不能为空")
		return
	}
	if len(dictData.DictLabel) > 100 {
		c.ErrorWithMessage(ctx, "字典标签长度不能超过100个字符")
		return
	}
	if len(dictData.DictValue) > 100 {
		c.ErrorWithMessage(ctx, "字典键值长度不能超过100个字符")
		return
	}
	if len(dictData.DictType) > 100 {
		c.ErrorWithMessage(ctx, "字典类型长度不能超过100个字符")
		return
	}
	if len(dictData.CssClass) > 100 {
		c.ErrorWithMessage(ctx, "样式属性长度不能超过100个字符")
		return
	}

	// 设置默认值
	dictData.CreateBy = c.GetUsername(ctx)
	if dictData.Status == "" {
		dictData.Status = "0" // 默认正常状态
	}
	if dictData.IsDefault == "" {
		dictData.IsDefault = "N" // 默认非默认值
	}

	// 保存字典数据
	if err := c.db.Create(&dictData).Error; err != nil {
		c.Logger.Error("新增字典数据失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改字典数据 - 完全按照Java后端业务逻辑
func (c *SysDictDataController) Edit(ctx *gin.Context) {
	var dictData domain.SysDictData
	if err := ctx.ShouldBindJSON(&dictData); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if dictData.DictCode == 0 {
		c.ErrorWithMessage(ctx, "字典编码不能为空")
		return
	}
	if dictData.DictLabel == "" {
		c.ErrorWithMessage(ctx, "字典标签不能为空")
		return
	}
	if dictData.DictValue == "" {
		c.ErrorWithMessage(ctx, "字典键值不能为空")
		return
	}
	if dictData.DictType == "" {
		c.ErrorWithMessage(ctx, "字典类型不能为空")
		return
	}
	if len(dictData.DictLabel) > 100 {
		c.ErrorWithMessage(ctx, "字典标签长度不能超过100个字符")
		return
	}
	if len(dictData.DictValue) > 100 {
		c.ErrorWithMessage(ctx, "字典键值长度不能超过100个字符")
		return
	}
	if len(dictData.DictType) > 100 {
		c.ErrorWithMessage(ctx, "字典类型长度不能超过100个字符")
		return
	}
	if len(dictData.CssClass) > 100 {
		c.ErrorWithMessage(ctx, "样式属性长度不能超过100个字符")
		return
	}

	// 检查字典数据是否存在
	var existingDictData domain.SysDictData
	if err := c.db.Where("dict_code = ?", dictData.DictCode).First(&existingDictData).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "字典数据不存在")
		} else {
			c.Logger.Error("查询字典数据失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 设置更新者
	dictData.UpdateBy = c.GetUsername(ctx)

	// 更新字典数据信息
	updateData := map[string]interface{}{
		"dict_sort":  dictData.DictSort,
		"dict_label": dictData.DictLabel,
		"dict_value": dictData.DictValue,
		"dict_type":  dictData.DictType,
		"css_class":  dictData.CssClass,
		"list_class": dictData.ListClass,
		"is_default": dictData.IsDefault,
		"status":     dictData.Status,
		"update_by":  dictData.UpdateBy,
		"remark":     dictData.Remark,
	}

	if err := c.db.Model(&existingDictData).Updates(updateData).Error; err != nil {
		c.Logger.Error("修改字典数据失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除字典数据 - 完全按照Java后端业务逻辑
func (c *SysDictDataController) Remove(ctx *gin.Context) {
	dictCodesStr := ctx.Param("dictCodes")
	dictCodeStrs := strings.Split(dictCodesStr, ",")

	var dictCodes []int64
	for _, dictCodeStr := range dictCodeStrs {
		dictCode, err := strconv.ParseInt(dictCodeStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "字典编码格式错误")
			return
		}
		dictCodes = append(dictCodes, dictCode)
	}

	// 删除字典数据
	if err := c.db.Where("dict_code IN ?", dictCodes).Delete(&domain.SysDictData{}).Error; err != nil {
		c.Logger.Error("删除字典数据失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// Export 导出字典数据 - 简化实现
func (c *SysDictDataController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysDictDataController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysDictDataController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysDictDataController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysDictDataController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
