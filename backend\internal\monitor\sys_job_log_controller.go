package monitor

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysJobLogController 定时任务调度日志控制器
type SysJobLogController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysJobLogController 创建定时任务日志控制器
func NewSysJobLogController(logger *zap.Logger, db *gorm.DB) *SysJobLogController {
	return &SysJobLogController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysJobLogController的路由
func (c *SysJobLogController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                    // 定时任务调度日志列表
	r.POST("/export", c.Export)               // 导出定时任务调度日志
	r.GET("/:jobLogId", c.GetInfo)            // 获取定时任务调度日志详细信息
	r.DELETE("/:jobLogIds", c.Remove)         // 删除定时任务调度日志
	r.DELETE("/clean", c.Clean)               // 清空定时任务调度日志
}

// List 获取定时任务调度日志列表 - 完全按照Java后端业务逻辑
func (c *SysJobLogController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var jobLogs []domain.SysJobLog
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysJobLog{})

	// 任务名称查询
	if jobName := ctx.Query("jobName"); jobName != "" {
		query = query.Where("job_name LIKE ?", "%"+jobName+"%")
	}

	// 任务组名查询
	if jobGroup := ctx.Query("jobGroup"); jobGroup != "" {
		query = query.Where("job_group = ?", jobGroup)
	}

	// 执行状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 调用目标字符串查询
	if invokeTarget := ctx.Query("invokeTarget"); invokeTarget != "" {
		query = query.Where("invoke_target LIKE ?", "%"+invokeTarget+"%")
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("start_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("start_time <= ?", endTime)
	}

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("job_log_id DESC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&jobLogs).Error; err != nil {
		c.Logger.Error("查询定时任务调度日志列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  jobLogs,
	})
}

// GetInfo 获取定时任务调度日志详细信息 - 完全按照Java后端业务逻辑
func (c *SysJobLogController) GetInfo(ctx *gin.Context) {
	jobLogIdStr := ctx.Param("jobLogId")
	jobLogId, err := strconv.ParseInt(jobLogIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "日志ID格式错误")
		return
	}

	var jobLog domain.SysJobLog
	if err := c.db.Where("job_log_id = ?", jobLogId).First(&jobLog).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "日志不存在")
		} else {
			c.Logger.Error("查询定时任务调度日志详细信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, jobLog)
}

// Remove 删除定时任务调度日志 - 完全按照Java后端业务逻辑
func (c *SysJobLogController) Remove(ctx *gin.Context) {
	jobLogIdsStr := ctx.Param("jobLogIds")
	jobLogIdStrs := strings.Split(jobLogIdsStr, ",")

	var jobLogIds []int64
	for _, jobLogIdStr := range jobLogIdStrs {
		jobLogId, err := strconv.ParseInt(jobLogIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "日志ID格式错误")
			return
		}
		jobLogIds = append(jobLogIds, jobLogId)
	}

	// 删除任务日志
	if err := c.db.Where("job_log_id IN ?", jobLogIds).Delete(&domain.SysJobLog{}).Error; err != nil {
		c.Logger.Error("删除定时任务调度日志失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// Clean 清空定时任务调度日志 - 完全按照Java后端业务逻辑
func (c *SysJobLogController) Clean(ctx *gin.Context) {
	// 清空任务调度日志表
	if err := c.db.Exec("TRUNCATE TABLE sys_job_log").Error; err != nil {
		c.Logger.Error("清空定时任务调度日志失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "清空失败")
		return
	}

	c.SuccessWithMessage(ctx, "清空成功")
}

// Export 导出定时任务调度日志 - 简化实现
func (c *SysJobLogController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysJobLogController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysJobLogController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysJobLogController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}
