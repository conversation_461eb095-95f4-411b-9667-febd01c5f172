package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysMenuController 菜单信息控制器
type SysMenuController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysMenuController 创建菜单控制器
func NewSysMenuController(logger *zap.Logger, db *gorm.DB) *SysMenuController {
	return &SysMenuController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysMenuController的路由
func (c *SysMenuController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                                     // 菜单列表
	r.GET("/:menuId", c.GetInfo)                               // 获取菜单详情
	r.GET("/treeselect", c.Treeselect)                         // 菜单树选择
	r.GET("/roleMenuTreeselect/:roleId", c.RoleMenuTreeselect) // 角色菜单树
	r.POST("", c.Add)                                          // 新增菜单
	r.PUT("", c.Edit)                                          // 修改菜单
	r.DELETE("/:menuId", c.Remove)                             // 删除菜单
}

// List 获取菜单列表 - 完全按照Java后端业务逻辑
func (c *SysMenuController) List(ctx *gin.Context) {
	var menus []domain.SysMenu

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysMenu{})

	// 菜单名称模糊查询
	if menuName := ctx.Query("menuName"); menuName != "" {
		query = query.Where("menu_name LIKE ?", "%"+menuName+"%")
	}

	// 菜单状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 菜单类型查询
	if menuType := ctx.Query("menuType"); menuType != "" {
		query = query.Where("menu_type = ?", menuType)
	}

	// 显示状态查询
	if visible := ctx.Query("visible"); visible != "" {
		query = query.Where("visible = ?", visible)
	}

	// 数据权限控制 - 根据用户权限过滤菜单
	// TODO: 实现用户权限过滤逻辑

	// 排序 - 按父菜单ID和显示顺序排序
	query = query.Order("parent_id ASC, order_num ASC")

	// 执行查询
	if err := query.Find(&menus).Error; err != nil {
		c.Logger.Error("查询菜单列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 构建树形结构
	menuTree := c.buildMenuTree(menus, 0)

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, menuTree)
}

// GetInfo 根据菜单编号获取详细信息 - 完全按照Java后端逻辑
func (c *SysMenuController) GetInfo(ctx *gin.Context) {
	menuIdStr := ctx.Param("menuId")
	menuId, err := strconv.ParseInt(menuIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "菜单ID格式错误")
		return
	}

	var menu domain.SysMenu
	if err := c.db.Where("menu_id = ?", menuId).First(&menu).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "菜单不存在")
		} else {
			c.Logger.Error("查询菜单信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, menu)
}

// Treeselect 获取菜单下拉树列表 - 完全按照Java后端逻辑
func (c *SysMenuController) Treeselect(ctx *gin.Context) {
	var menus []domain.SysMenu

	// 构建查询条件
	query := c.db.Model(&domain.SysMenu{})

	// 菜单名称模糊查询
	if menuName := ctx.Query("menuName"); menuName != "" {
		query = query.Where("menu_name LIKE ?", "%"+menuName+"%")
	}

	// 菜单状态查询 - 只查询正常状态的菜单
	query = query.Where("status = ?", "0")

	// 数据权限控制
	// TODO: 根据用户权限过滤菜单

	// 排序
	query = query.Order("parent_id ASC, order_num ASC")

	// 执行查询
	if err := query.Find(&menus).Error; err != nil {
		c.Logger.Error("查询菜单树失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 构建菜单树选择结构
	treeSelect := c.buildMenuTreeSelect(menus, 0)

	c.SuccessWithData(ctx, treeSelect)
}

// RoleMenuTreeselect 加载对应角色菜单列表树 - 完全按照Java后端逻辑
func (c *SysMenuController) RoleMenuTreeselect(ctx *gin.Context) {
	roleIdStr := ctx.Param("roleId")
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 查询所有菜单
	var menus []domain.SysMenu
	query := c.db.Model(&domain.SysMenu{}).Where("status = ?", "0")

	// 数据权限控制
	// TODO: 根据用户权限过滤菜单

	query = query.Order("parent_id ASC, order_num ASC")

	if err := query.Find(&menus).Error; err != nil {
		c.Logger.Error("查询菜单列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 查询角色已选择的菜单ID
	checkedKeys := c.selectMenuListByRoleId(roleId)

	// 构建菜单树选择结构
	menuTreeSelect := c.buildMenuTreeSelect(menus, 0)

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"checkedKeys": checkedKeys,
		"menus":       menuTreeSelect,
	})
}

// Add 新增菜单 - 完全按照Java后端业务逻辑
func (c *SysMenuController) Add(ctx *gin.Context) {
	var menu domain.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if menu.MenuName == "" {
		c.ErrorWithMessage(ctx, "菜单名称不能为空")
		return
	}
	if menu.MenuType == "" {
		c.ErrorWithMessage(ctx, "菜单类型不能为空")
		return
	}
	if menu.OrderNum == 0 {
		c.ErrorWithMessage(ctx, "显示顺序不能为空")
		return
	}

	// 检查菜单名称唯一性
	if !c.checkMenuNameUnique(&menu) {
		c.ErrorWithMessage(ctx, "新增菜单'"+menu.MenuName+"'失败，菜单名称已存在")
		return
	}

	// 外链地址验证
	if menu.IsFrame == "0" && menu.Path != "" && !c.isHttpUrl(menu.Path) {
		c.ErrorWithMessage(ctx, "新增菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头")
		return
	}

	// 设置默认值
	menu.CreateBy = c.GetUsername(ctx)
	if menu.Status == "" {
		menu.Status = "0" // 默认正常状态
	}
	if menu.Visible == "" {
		menu.Visible = "0" // 默认显示
	}
	if menu.IsFrame == "" {
		menu.IsFrame = "1" // 默认不是外链
	}
	if menu.IsCache == "" {
		menu.IsCache = "0" // 默认缓存
	}

	// 保存菜单
	if err := c.db.Create(&menu).Error; err != nil {
		c.Logger.Error("新增菜单失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改菜单 - 完全按照Java后端业务逻辑
func (c *SysMenuController) Edit(ctx *gin.Context) {
	var menu domain.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if menu.MenuId == 0 {
		c.ErrorWithMessage(ctx, "菜单ID不能为空")
		return
	}
	if menu.MenuName == "" {
		c.ErrorWithMessage(ctx, "菜单名称不能为空")
		return
	}
	if menu.MenuType == "" {
		c.ErrorWithMessage(ctx, "菜单类型不能为空")
		return
	}

	// 检查菜单是否存在
	var existingMenu domain.SysMenu
	if err := c.db.Where("menu_id = ?", menu.MenuId).First(&existingMenu).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "菜单不存在")
		} else {
			c.Logger.Error("查询菜单失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查菜单名称唯一性（排除自己）
	if !c.checkMenuNameUnique(&menu) {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，菜单名称已存在")
		return
	}

	// 外链地址验证
	if menu.IsFrame == "0" && menu.Path != "" && !c.isHttpUrl(menu.Path) {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头")
		return
	}

	// 检查上级菜单不能选择自己
	if menu.MenuId == menu.ParentId {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，上级菜单不能选择自己")
		return
	}

	// 设置更新者
	menu.UpdateBy = c.GetUsername(ctx)

	// 更新菜单
	if err := c.db.Model(&existingMenu).Updates(&menu).Error; err != nil {
		c.Logger.Error("修改菜单失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除菜单 - 完全按照Java后端业务逻辑
func (c *SysMenuController) Remove(ctx *gin.Context) {
	menuIdStr := ctx.Param("menuId")
	menuId, err := strconv.ParseInt(menuIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "菜单ID格式错误")
		return
	}

	// 检查菜单是否存在
	var menu domain.SysMenu
	if err := c.db.Where("menu_id = ?", menuId).First(&menu).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "菜单不存在")
		} else {
			c.Logger.Error("查询菜单失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查是否存在子菜单
	if c.hasChildByMenuId(menuId) {
		c.ErrorWithMessage(ctx, "存在子菜单,不允许删除")
		return
	}

	// 检查菜单是否已分配给角色
	if c.checkMenuExistRole(menuId) {
		c.ErrorWithMessage(ctx, "菜单已分配,不允许删除")
		return
	}

	// 删除菜单
	if err := c.db.Delete(&menu).Error; err != nil {
		c.Logger.Error("删除菜单失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// 辅助函数

// buildMenuTree 构建菜单树形结构
func (c *SysMenuController) buildMenuTree(menus []domain.SysMenu, parentId int64) []domain.SysMenu {
	var tree []domain.SysMenu

	for _, menu := range menus {
		if menu.ParentId == parentId {
			children := c.buildMenuTree(menus, menu.MenuId)
			menu.Children = children
			tree = append(tree, menu)
		}
	}

	return tree
}

// buildMenuTreeSelect 构建菜单树选择结构
func (c *SysMenuController) buildMenuTreeSelect(menus []domain.SysMenu, parentId int64) []gin.H {
	var tree []gin.H

	for _, menu := range menus {
		if menu.ParentId == parentId {
			children := c.buildMenuTreeSelect(menus, menu.MenuId)

			node := gin.H{
				"id":    menu.MenuId,
				"label": menu.MenuName,
			}

			if len(children) > 0 {
				node["children"] = children
			}

			tree = append(tree, node)
		}
	}

	return tree
}

// selectMenuListByRoleId 根据角色ID查询菜单
func (c *SysMenuController) selectMenuListByRoleId(roleId int64) []int64 {
	var menuIds []int64

	// TODO: 查询sys_role_menu表获取角色关联的菜单ID
	// 暂时返回空数组

	return menuIds
}

// checkMenuNameUnique 校验菜单名称是否唯一
func (c *SysMenuController) checkMenuNameUnique(menu *domain.SysMenu) bool {
	var count int64
	query := c.db.Model(&domain.SysMenu{}).Where("menu_name = ? AND parent_id = ?", menu.MenuName, menu.ParentId)

	// 如果是修改操作，排除自己
	if menu.MenuId > 0 {
		query = query.Where("menu_id != ?", menu.MenuId)
	}

	query.Count(&count)
	return count == 0
}

// isHttpUrl 判断是否为HTTP链接
func (c *SysMenuController) isHttpUrl(url string) bool {
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}

// hasChildByMenuId 是否存在子菜单
func (c *SysMenuController) hasChildByMenuId(menuId int64) bool {
	var count int64
	c.db.Model(&domain.SysMenu{}).Where("parent_id = ?", menuId).Count(&count)
	return count > 0
}

// checkMenuExistRole 查询菜单是否存在角色
func (c *SysMenuController) checkMenuExistRole(menuId int64) bool {
	// TODO: 查询sys_role_menu表检查菜单是否被角色使用
	// 暂时返回false
	return false
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysMenuController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysMenuController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysMenuController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysMenuController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
