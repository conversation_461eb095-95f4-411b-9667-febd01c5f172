# JWT测试脚本
Write-Host "🔐 WOSM JWT认证测试" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

$baseUrl = "http://localhost:8080"

# 1. 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "✅ 健康检查成功: $($healthResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试验证码接口
Write-Host "`n2. 测试验证码接口..." -ForegroundColor Yellow
try {
    $captchaResponse = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method GET
    Write-Host "✅ 验证码接口成功: $($captchaResponse.code)" -ForegroundColor Green
} catch {
    Write-Host "❌ 验证码接口失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试登录接口
Write-Host "`n3. 测试登录接口..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123"
    code = ""
    uuid = ""
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
    if ($loginResponse.code -eq 200) {
        $token = $loginResponse.data.token
        Write-Host "✅ 登录成功!" -ForegroundColor Green
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Cyan
    } else {
        Write-Host "❌ 登录失败: $($loginResponse.msg)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. 测试获取用户信息（不带Token）
Write-Host "`n4. 测试获取用户信息（不带Token）..." -ForegroundColor Yellow
try {
    $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET
    Write-Host "❌ 应该返回401，但返回了: $($userInfoResponse.code)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ 正确返回401未授权" -ForegroundColor Green
    } else {
        Write-Host "❌ 意外错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 测试获取用户信息（带Token）
Write-Host "`n5. 测试获取用户信息（带Token）..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
    if ($userInfoResponse.code -eq 200) {
        Write-Host "✅ 获取用户信息成功!" -ForegroundColor Green
        Write-Host "   用户名: $($userInfoResponse.data.user.loginName)" -ForegroundColor Cyan
        Write-Host "   用户ID: $($userInfoResponse.data.user.userId)" -ForegroundColor Cyan
        Write-Host "   角色: $($userInfoResponse.data.roles -join ', ')" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 获取用户信息失败: $($userInfoResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取用户信息请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "   状态码: $statusCode" -ForegroundColor Red
    }
}

# 6. 测试获取路由信息
Write-Host "`n6. 测试获取路由信息..." -ForegroundColor Yellow
try {
    $routersResponse = Invoke-RestMethod -Uri "$baseUrl/getRouters" -Method GET -Headers $headers
    if ($routersResponse.code -eq 200) {
        Write-Host "✅ 获取路由信息成功!" -ForegroundColor Green
        Write-Host "   路由数量: $($routersResponse.data.Count)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 获取路由信息失败: $($routersResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取路由信息请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试登出接口
Write-Host "`n7. 测试登出接口..." -ForegroundColor Yellow
try {
    $logoutResponse = Invoke-RestMethod -Uri "$baseUrl/logout" -Method POST -Headers $headers
    if ($logoutResponse.code -eq 200) {
        Write-Host "✅ 登出成功!" -ForegroundColor Green
    } else {
        Write-Host "❌ 登出失败: $($logoutResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 登出请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 JWT认证测试完成!" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
