# WOSM Go后端自动化部署脚本
# 版本: 1.0
# 作者: Augment Agent
# 日期: 2025-06-13

param(
    [string]$Environment = "production",
    [string]$Version = "1.0.0",
    [switch]$SkipTests = $false,
    [switch]$SkipBackup = $false,
    [switch]$Force = $false
)

# 配置变量
$ProjectRoot = "D:\wosm\backend"
$BackupDir = "D:\wosm\backup"
$LogFile = "deploy.log"
$ServiceName = "wosm"
$Port = 8080

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
    Add-Content -Path $LogFile -Value "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - $Message"
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# 错误处理
$ErrorActionPreference = "Stop"
trap {
    Write-Error "部署失败: $_"
    exit 1
}

Write-Info "=== WOSM Go后端自动化部署开始 ==="
Write-Info "环境: $Environment"
Write-Info "版本: $Version"
Write-Info "时间: $(Get-Date)"

# 1. 环境检查
Write-Info "1. 检查部署环境..."

# 检查Go环境
try {
    $goVersion = go version
    Write-Success "Go环境检查通过: $goVersion"
} catch {
    Write-Error "Go环境未安装或配置错误"
    exit 1
}

# 检查项目目录
if (-not (Test-Path $ProjectRoot)) {
    Write-Error "项目目录不存在: $ProjectRoot"
    exit 1
}
Write-Success "项目目录检查通过"

# 检查数据库连接
try {
    $dbTest = sqlcmd -S localhost -U sa -P "F@2233" -Q "SELECT 1" -h -1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "数据库连接检查通过"
    } else {
        Write-Error "数据库连接失败"
        exit 1
    }
} catch {
    Write-Error "数据库连接检查失败: $_"
    exit 1
}

# 2. 停止现有服务
Write-Info "2. 停止现有服务..."
try {
    $process = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
    if ($process) {
        Write-Warning "发现运行中的服务，正在停止..."
        Stop-Process -Name $ServiceName -Force
        Start-Sleep -Seconds 3
        Write-Success "服务已停止"
    } else {
        Write-Info "没有发现运行中的服务"
    }
} catch {
    Write-Warning "停止服务时出现警告: $_"
}

# 3. 备份现有版本
if (-not $SkipBackup) {
    Write-Info "3. 备份现有版本..."
    
    $backupTimestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupPath = Join-Path $BackupDir "wosm_backup_$backupTimestamp"
    
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    }
    
    if (Test-Path "$ProjectRoot\wosm.exe") {
        Copy-Item "$ProjectRoot\wosm.exe" "$backupPath.exe" -Force
        Write-Success "可执行文件已备份到: $backupPath.exe"
    }
    
    if (Test-Path "$ProjectRoot\configs") {
        Copy-Item "$ProjectRoot\configs" "$backupPath_configs" -Recurse -Force
        Write-Success "配置文件已备份到: $backupPath_configs"
    }
} else {
    Write-Warning "3. 跳过备份步骤"
}

# 4. 代码检查和测试
if (-not $SkipTests) {
    Write-Info "4. 运行代码检查和测试..."
    
    Set-Location $ProjectRoot
    
    # 代码格式检查
    Write-Info "检查代码格式..."
    $fmtResult = go fmt ./...
    if ($fmtResult) {
        Write-Warning "代码格式已自动修复"
    } else {
        Write-Success "代码格式检查通过"
    }
    
    # 代码静态分析
    Write-Info "运行静态分析..."
    try {
        go vet ./...
        Write-Success "静态分析通过"
    } catch {
        Write-Error "静态分析发现问题: $_"
        if (-not $Force) {
            exit 1
        }
    }
    
    # 运行测试
    Write-Info "运行单元测试..."
    try {
        go test ./... -v
        Write-Success "单元测试通过"
    } catch {
        Write-Error "单元测试失败: $_"
        if (-not $Force) {
            exit 1
        }
    }
} else {
    Write-Warning "4. 跳过测试步骤"
}

# 5. 编译应用
Write-Info "5. 编译应用程序..."

Set-Location $ProjectRoot

# 设置编译环境变量
$env:CGO_ENABLED = "1"
$env:GOOS = "windows"
$env:GOARCH = "amd64"

# 编译
try {
    $buildCmd = "go build -ldflags `"-X main.Version=$Version -X main.BuildTime=$(Get-Date -Format 'yyyy-MM-dd_HH:mm:ss')`" -o wosm.exe main.go"
    Invoke-Expression $buildCmd
    
    if (Test-Path "wosm.exe") {
        $fileInfo = Get-Item "wosm.exe"
        Write-Success "编译成功，文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
    } else {
        Write-Error "编译失败，未生成可执行文件"
        exit 1
    }
} catch {
    Write-Error "编译失败: $_"
    exit 1
}

# 6. 配置检查
Write-Info "6. 检查配置文件..."

$configFile = "configs\config.yaml"
if (Test-Path $configFile) {
    Write-Success "配置文件存在: $configFile"
    
    # 检查配置文件语法
    try {
        $config = Get-Content $configFile -Raw | ConvertFrom-Yaml -ErrorAction Stop
        Write-Success "配置文件语法正确"
    } catch {
        Write-Error "配置文件语法错误: $_"
        exit 1
    }
} else {
    Write-Error "配置文件不存在: $configFile"
    exit 1
}

# 7. 启动服务
Write-Info "7. 启动新服务..."

try {
    # 启动服务
    $process = Start-Process -FilePath ".\wosm.exe" -PassThru -WindowStyle Hidden
    
    if ($process) {
        Write-Success "服务已启动，进程ID: $($process.Id)"
        
        # 等待服务启动
        Write-Info "等待服务启动..."
        Start-Sleep -Seconds 10
        
        # 健康检查
        $maxRetries = 30
        $retryCount = 0
        $healthCheckPassed = $false
        
        while ($retryCount -lt $maxRetries -and -not $healthCheckPassed) {
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:$Port/health" -TimeoutSec 5
                if ($response.status -eq "up") {
                    $healthCheckPassed = $true
                    Write-Success "健康检查通过"
                } else {
                    throw "健康检查返回状态: $($response.status)"
                }
            } catch {
                $retryCount++
                Write-Warning "健康检查失败 ($retryCount/$maxRetries): $_"
                Start-Sleep -Seconds 2
            }
        }
        
        if (-not $healthCheckPassed) {
            Write-Error "健康检查失败，服务可能未正常启动"
            exit 1
        }
    } else {
        Write-Error "服务启动失败"
        exit 1
    }
} catch {
    Write-Error "启动服务时出错: $_"
    exit 1
}

# 8. 功能验证
Write-Info "8. 运行功能验证..."

try {
    # 登录测试
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:$Port/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    
    if ($loginResponse.code -eq 200 -and $loginResponse.data.token) {
        Write-Success "登录功能验证通过"
        
        # API测试
        $headers = @{ Authorization = "Bearer $($loginResponse.data.token)" }
        $userResponse = Invoke-RestMethod -Uri "http://localhost:$Port/api/getInfo" -Headers $headers -TimeoutSec 10
        
        if ($userResponse.code -eq 200) {
            Write-Success "API功能验证通过"
        } else {
            Write-Warning "API功能验证失败"
        }
    } else {
        Write-Warning "登录功能验证失败"
    }
} catch {
    Write-Warning "功能验证出现异常: $_"
}

# 9. 清理工作
Write-Info "9. 执行清理工作..."

# 清理临时文件
if (Test-Path "*.tmp") {
    Remove-Item "*.tmp" -Force
    Write-Success "临时文件已清理"
}

# 清理旧日志
$logFiles = Get-ChildItem "logs\*.log" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-30) }
if ($logFiles) {
    $logFiles | Remove-Item -Force
    Write-Success "旧日志文件已清理: $($logFiles.Count) 个文件"
}

# 10. 部署总结
Write-Info "10. 部署总结"

$deployEndTime = Get-Date
Write-Success "=== 部署完成 ==="
Write-Info "部署版本: $Version"
Write-Info "部署环境: $Environment"
Write-Info "完成时间: $deployEndTime"
Write-Info "服务地址: http://localhost:$Port"

# 显示服务状态
$process = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
if ($process) {
    Write-Success "服务运行状态: 正常"
    Write-Info "进程ID: $($process.Id)"
    Write-Info "内存使用: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB"
} else {
    Write-Warning "服务运行状态: 异常"
}

Write-Success "部署脚本执行完成！"

# 可选：打开浏览器验证
if ($Environment -eq "development") {
    Write-Info "正在打开浏览器进行验证..."
    Start-Process "http://localhost:$Port/health"
}
