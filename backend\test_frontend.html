<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WOSM 前端集成测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 10px 0; }
        input, button { padding: 8px; margin: 5px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .info { background: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; }
        .hidden { display: none; }
        #log { background: #f8f9fa; padding: 10px; border: 1px solid #ddd; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WOSM Go后端集成测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong>这个页面用于测试WOSM Go后端的前端集成。请确保后端服务运行在 http://localhost:8080
        </div>

        <!-- 登录表单 -->
        <div id="login-section">
            <h2>🔐 用户登录</h2>
            <div class="form-group">
                <input type="text" id="username" placeholder="用户名" value="admin">
            </div>
            <div class="form-group">
                <input type="password" id="password" placeholder="密码" value="admin123">
            </div>
            <div class="form-group">
                <button onclick="testLogin()">登录测试</button>
                <button onclick="testCaptcha()">获取验证码</button>
            </div>
        </div>

        <!-- 用户信息区域 -->
        <div id="user-section" class="hidden">
            <h2>👤 用户信息</h2>
            <div id="user-details"></div>
            <div class="form-group">
                <button onclick="testGetInfo()">获取用户信息</button>
                <button onclick="testGetRouters()">获取菜单路由</button>
                <button onclick="testLogout()">登出</button>
            </div>
        </div>

        <!-- API测试区域 -->
        <div id="api-section" class="hidden">
            <h2>🔧 API接口测试</h2>
            <div class="form-group">
                <button onclick="testUserList()">用户列表</button>
                <button onclick="testRoleList()">角色列表</button>
                <button onclick="testMenuList()">菜单列表</button>
                <button onclick="testHealth()">健康检查</button>
            </div>
        </div>

        <!-- 日志区域 -->
        <h2>📋 测试日志</h2>
        <div id="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            try {
                log(`发送请求: ${options.method || 'GET'} ${url}`);
                
                const response = await fetch(url, options);
                const data = await response.json();
                
                log(`响应状态: ${response.status}`);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                return { response, data };
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 带认证的请求
        async function makeAuthRequest(url, options = {}) {
            const token = localStorage.getItem('token');
            
            if (!token) {
                log('未找到token，请先登录', 'error');
                return;
            }

            options.headers = options.headers || {};
            options.headers['Authorization'] = `Bearer ${token}`;
            options.headers['Content-Type'] = 'application/json';
            
            log(`使用token: ${token.substring(0, 50)}...`);
            
            return makeRequest(url, options);
        }

        // 测试验证码
        async function testCaptcha() {
            try {
                const result = await makeRequest('/captchaImage');
                if (result.response.status === 200) {
                    log('✅ 验证码接口正常', 'success');
                } else {
                    log('❌ 验证码接口异常', 'error');
                }
            } catch (error) {
                log('❌ 验证码请求失败', 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const loginData = {
                username: username,
                password: password,
                code: '',
                uuid: ''
            };

            try {
                const result = await makeRequest('/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });

                if (result.data.code === 200 && result.data.data && result.data.data.token) {
                    const token = result.data.data.token;
                    localStorage.setItem('token', token);
                    localStorage.setItem('userInfo', JSON.stringify(result.data.data.user));
                    
                    log('✅ 登录成功！', 'success');
                    log(`Token已保存: ${token.substring(0, 50)}...`);
                    
                    // 显示用户区域
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('user-section').classList.remove('hidden');
                    document.getElementById('api-section').classList.remove('hidden');
                    
                    // 显示用户信息
                    const user = result.data.data.user;
                    document.getElementById('user-details').innerHTML = `
                        <p><strong>用户ID:</strong> ${user.userId}</p>
                        <p><strong>用户名:</strong> ${user.loginName}</p>
                        <p><strong>姓名:</strong> ${user.userName}</p>
                        <p><strong>邮箱:</strong> ${user.email}</p>
                    `;
                } else {
                    log(`❌ 登录失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 登录请求失败', 'error');
            }
        }

        // 测试获取用户信息
        async function testGetInfo() {
            try {
                const result = await makeAuthRequest('/getInfo');
                if (result && result.data.code === 200) {
                    log('✅ 获取用户信息成功', 'success');
                    log(`用户: ${result.data.data.user.loginName}`);
                } else if (result) {
                    log(`❌ 获取用户信息失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 获取用户信息请求失败', 'error');
            }
        }

        // 测试获取路由
        async function testGetRouters() {
            try {
                const result = await makeAuthRequest('/getRouters');
                if (result && result.data.code === 200) {
                    log('✅ 获取菜单路由成功', 'success');
                    log(`路由数量: ${result.data.data.length}`);
                } else if (result) {
                    log(`❌ 获取菜单路由失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 获取菜单路由请求失败', 'error');
            }
        }

        // 测试用户列表
        async function testUserList() {
            try {
                const result = await makeAuthRequest('/api/system/user/list');
                if (result && result.data.code === 200) {
                    log('✅ 获取用户列表成功', 'success');
                    log(`用户数量: ${result.data.data.total}`);
                } else if (result) {
                    log(`❌ 获取用户列表失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 获取用户列表请求失败', 'error');
            }
        }

        // 测试角色列表
        async function testRoleList() {
            try {
                const result = await makeAuthRequest('/api/system/role/list');
                if (result && result.data.code === 200) {
                    log('✅ 获取角色列表成功', 'success');
                } else if (result) {
                    log(`❌ 获取角色列表失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 获取角色列表请求失败', 'error');
            }
        }

        // 测试菜单列表
        async function testMenuList() {
            try {
                const result = await makeAuthRequest('/api/system/menu/list');
                if (result && result.data.code === 200) {
                    log('✅ 获取菜单列表成功', 'success');
                } else if (result) {
                    log(`❌ 获取菜单列表失败: ${result.data.msg}`, 'error');
                }
            } catch (error) {
                log('❌ 获取菜单列表请求失败', 'error');
            }
        }

        // 测试健康检查
        async function testHealth() {
            try {
                const result = await makeRequest('/health');
                if (result.response.status === 200) {
                    log('✅ 健康检查正常', 'success');
                    log(`服务: ${result.data.service} v${result.data.version}`);
                } else {
                    log('❌ 健康检查异常', 'error');
                }
            } catch (error) {
                log('❌ 健康检查请求失败', 'error');
            }
        }

        // 测试登出
        async function testLogout() {
            try {
                const result = await makeAuthRequest('/logout', { method: 'POST' });
                
                // 清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('userInfo');
                
                // 重置界面
                document.getElementById('login-section').classList.remove('hidden');
                document.getElementById('user-section').classList.add('hidden');
                document.getElementById('api-section').classList.add('hidden');
                document.getElementById('user-details').innerHTML = '';
                
                log('✅ 登出成功', 'success');
            } catch (error) {
                log('❌ 登出请求失败', 'error');
            }
        }

        // 页面加载时检查是否已登录
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                log('发现已保存的token，尝试验证...');
                testGetInfo().then(() => {
                    // 如果验证成功，显示用户界面
                    const userInfo = localStorage.getItem('userInfo');
                    if (userInfo) {
                        const user = JSON.parse(userInfo);
                        document.getElementById('login-section').classList.add('hidden');
                        document.getElementById('user-section').classList.remove('hidden');
                        document.getElementById('api-section').classList.remove('hidden');
                        
                        document.getElementById('user-details').innerHTML = `
                            <p><strong>用户ID:</strong> ${user.userId}</p>
                            <p><strong>用户名:</strong> ${user.loginName}</p>
                            <p><strong>姓名:</strong> ${user.userName}</p>
                            <p><strong>邮箱:</strong> ${user.email}</p>
                        `;
                    }
                });
            }
            
            log('🚀 WOSM前端集成测试页面已加载');
            log('请确保后端服务运行在 http://localhost:8080');
        };
    </script>
</body>
</html>
