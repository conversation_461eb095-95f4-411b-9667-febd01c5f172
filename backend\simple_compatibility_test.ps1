# 简化的前端兼容性测试
Write-Host "前端兼容性测试开始..." -ForegroundColor Green

$baseUrl = "http://localhost:8080"

# 1. 测试验证码
Write-Host "1. 测试验证码接口..." -ForegroundColor Yellow
$captchaResponse = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method GET
Write-Host "验证码响应:" -ForegroundColor Cyan
Write-Host ($captchaResponse | ConvertTo-Json) -ForegroundColor White

# 2. 测试登录
Write-Host "2. 测试登录接口..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123"
    code = ""
    uuid = ""
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
Write-Host "登录响应:" -ForegroundColor Cyan
Write-Host ($loginResponse | ConvertTo-Json) -ForegroundColor White

# 检查token位置
if ($loginResponse.token) {
    Write-Host "根级别token存在" -ForegroundColor Green
    $token = $loginResponse.token
} elseif ($loginResponse.data.token) {
    Write-Host "data.token存在" -ForegroundColor Green
    $token = $loginResponse.data.token
} else {
    Write-Host "未找到token" -ForegroundColor Red
    exit
}

# 3. 测试用户信息
Write-Host "3. 测试用户信息接口..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/getInfo" -Method GET -Headers $headers
    Write-Host "用户信息响应:" -ForegroundColor Cyan
    Write-Host ($userInfoResponse | ConvertTo-Json) -ForegroundColor White
} catch {
    Write-Host "用户信息请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "测试完成!" -ForegroundColor Green
