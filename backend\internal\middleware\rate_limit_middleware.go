package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
	Reset(key string)
}

// TokenBucketLimiter 令牌桶限流器
type TokenBucketLimiter struct {
	buckets map[string]*TokenBucket
	mutex   sync.RWMutex
	rate    int           // 每秒生成的令牌数
	burst   int           // 桶的容量
	cleanup time.Duration // 清理间隔
}

// TokenBucket 令牌桶
type TokenBucket struct {
	tokens   int       // 当前令牌数
	capacity int       // 桶容量
	rate     int       // 令牌生成速率
	lastTime time.Time // 上次更新时间
	mutex    sync.Mutex
}

// NewTokenBucketLimiter 创建令牌桶限流器
func NewTokenBucketLimiter(rate, burst int, cleanup time.Duration) *TokenBucketLimiter {
	limiter := &TokenBucketLimiter{
		buckets: make(map[string]*TokenBucket),
		rate:    rate,
		burst:   burst,
		cleanup: cleanup,
	}

	// 启动清理协程
	go limiter.cleanupExpiredBuckets()

	return limiter
}

// Allow 检查是否允许请求
func (l *TokenBucketLimiter) Allow(key string) bool {
	l.mutex.RLock()
	bucket, exists := l.buckets[key]
	l.mutex.RUnlock()

	if !exists {
		l.mutex.Lock()
		// 双重检查
		if bucket, exists = l.buckets[key]; !exists {
			bucket = &TokenBucket{
				tokens:   l.burst,
				capacity: l.burst,
				rate:     l.rate,
				lastTime: time.Now(),
			}
			l.buckets[key] = bucket
		}
		l.mutex.Unlock()
	}

	return bucket.takeToken()
}

// Reset 重置限流器
func (l *TokenBucketLimiter) Reset(key string) {
	l.mutex.Lock()
	delete(l.buckets, key)
	l.mutex.Unlock()
}

// takeToken 获取令牌
func (b *TokenBucket) takeToken() bool {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	now := time.Now()
	elapsed := now.Sub(b.lastTime)

	// 计算应该添加的令牌数
	tokensToAdd := int(elapsed.Seconds()) * b.rate
	if tokensToAdd > 0 {
		b.tokens += tokensToAdd
		if b.tokens > b.capacity {
			b.tokens = b.capacity
		}
		b.lastTime = now
	}

	// 检查是否有可用令牌
	if b.tokens > 0 {
		b.tokens--
		return true
	}

	return false
}

// cleanupExpiredBuckets 清理过期的桶
func (l *TokenBucketLimiter) cleanupExpiredBuckets() {
	ticker := time.NewTicker(l.cleanup)
	defer ticker.Stop()

	for range ticker.C {
		l.mutex.Lock()
		now := time.Now()
		for key, bucket := range l.buckets {
			bucket.mutex.Lock()
			if now.Sub(bucket.lastTime) > l.cleanup*2 {
				delete(l.buckets, key)
			}
			bucket.mutex.Unlock()
		}
		l.mutex.Unlock()
	}
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Rate    int                       // 每秒请求数
	Burst   int                       // 突发请求数
	KeyFunc func(*gin.Context) string // 生成限流键的函数
	Message string                    // 限流消息
	Cleanup time.Duration             // 清理间隔
}

// DefaultKeyFunc 默认键生成函数（基于IP）
func DefaultKeyFunc(c *gin.Context) string {
	return c.ClientIP()
}

// UserKeyFunc 基于用户的键生成函数
func UserKeyFunc(c *gin.Context) string {
	userID := c.GetString("userId")
	if userID == "" {
		return c.ClientIP()
	}
	return fmt.Sprintf("user:%s", userID)
}

// APIKeyFunc 基于API路径的键生成函数
func APIKeyFunc(c *gin.Context) string {
	return fmt.Sprintf("%s:%s", c.ClientIP(), c.Request.URL.Path)
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(config RateLimitConfig, logger *zap.Logger) gin.HandlerFunc {
	if config.Rate <= 0 {
		config.Rate = 100 // 默认每秒100个请求
	}
	if config.Burst <= 0 {
		config.Burst = config.Rate * 2 // 默认突发为速率的2倍
	}
	if config.KeyFunc == nil {
		config.KeyFunc = DefaultKeyFunc
	}
	if config.Message == "" {
		config.Message = "请求过于频繁，请稍后再试"
	}
	if config.Cleanup <= 0 {
		config.Cleanup = 5 * time.Minute // 默认5分钟清理一次
	}

	limiter := NewTokenBucketLimiter(config.Rate, config.Burst, config.Cleanup)

	return func(c *gin.Context) {
		key := config.KeyFunc(c)

		if !limiter.Allow(key) {
			logger.Warn("请求被限流",
				zap.String("ip", c.ClientIP()),
				zap.String("url", c.Request.URL.String()),
				zap.String("method", c.Request.Method),
				zap.String("key", key),
				zap.String("userAgent", c.Request.UserAgent()))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"code": 429,
				"msg":  config.Message,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// LoginRateLimitMiddleware 登录限流中间件
func LoginRateLimitMiddleware(logger *zap.Logger) gin.HandlerFunc {
	config := RateLimitConfig{
		Rate:  5,  // 每秒5次登录尝试
		Burst: 10, // 突发10次
		KeyFunc: func(c *gin.Context) string {
			return fmt.Sprintf("login:%s", c.ClientIP())
		},
		Message: "登录尝试过于频繁，请稍后再试",
		Cleanup: 10 * time.Minute,
	}

	return RateLimitMiddleware(config, logger)
}

// APIRateLimitMiddleware API限流中间件
func APIRateLimitMiddleware(logger *zap.Logger) gin.HandlerFunc {
	config := RateLimitConfig{
		Rate:    50,  // 每秒50个API请求
		Burst:   100, // 突发100个
		KeyFunc: DefaultKeyFunc,
		Message: "API请求过于频繁，请稍后再试",
		Cleanup: 5 * time.Minute,
	}

	return RateLimitMiddleware(config, logger)
}

// UserAPIRateLimitMiddleware 用户API限流中间件
func UserAPIRateLimitMiddleware(logger *zap.Logger) gin.HandlerFunc {
	config := RateLimitConfig{
		Rate:    50,  // 每个用户每秒50个请求
		Burst:   100, // 突发100个
		KeyFunc: UserKeyFunc,
		Message: "用户请求过于频繁，请稍后再试",
		Cleanup: 5 * time.Minute,
	}

	return RateLimitMiddleware(config, logger)
}

// SensitiveAPIRateLimitMiddleware 敏感API限流中间件
func SensitiveAPIRateLimitMiddleware(logger *zap.Logger) gin.HandlerFunc {
	config := RateLimitConfig{
		Rate:  10, // 敏感操作每秒10个请求
		Burst: 20, // 突发20个
		KeyFunc: func(c *gin.Context) string {
			return fmt.Sprintf("sensitive:%s:%s", c.ClientIP(), c.Request.URL.Path)
		},
		Message: "敏感操作过于频繁，请稍后再试",
		Cleanup: 10 * time.Minute,
	}

	return RateLimitMiddleware(config, logger)
}
