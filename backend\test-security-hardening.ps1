# WOSM Security Hardening Test

Write-Host "=== WOSM Security Hardening Test ===" -ForegroundColor Blue
Write-Host "Testing security middleware and protection mechanisms" -ForegroundColor Gray
Write-Host ""

$baseUrl = "http://localhost:8080/api"
$testResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Details = @()
}

# Helper function to record test results
function Record-TestResult {
    param($TestName, $Success, $Message)
    $testResults.Total++
    if ($Success) {
        $testResults.Passed++
        Write-Host "✅ $TestName" -ForegroundColor Green
    } else {
        $testResults.Failed++
        Write-Host "❌ $TestName - $Message" -ForegroundColor Red
    }
    $testResults.Details += @{Name=$TestName; Success=$Success; Message=$Message}
}

# Test 1: Security Headers
Write-Host "1. Testing Security Headers..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/getInfo" -Method Get -TimeoutSec 10 -ErrorAction Stop
    
    $securityHeaders = @{
        "X-Frame-Options" = "DENY"
        "X-Content-Type-Options" = "nosniff"
        "X-XSS-Protection" = "1; mode=block"
        "Content-Security-Policy" = $null
        "Referrer-Policy" = "strict-origin-when-cross-origin"
    }
    
    $allHeadersPresent = $true
    foreach ($header in $securityHeaders.Keys) {
        if (-not $response.Headers[$header]) {
            $allHeadersPresent = $false
            break
        }
    }
    
    Record-TestResult "Security Headers" $allHeadersPresent "Security headers are properly set"
} catch {
    Record-TestResult "Security Headers" $false "Failed to test security headers: $_"
}

# Test 2: SQL Injection Protection
Write-Host ""
Write-Host "2. Testing SQL Injection Protection..." -ForegroundColor Yellow

$sqlInjectionPayloads = @(
    "' OR '1'='1",
    "'; DROP TABLE users; --",
    "' UNION SELECT * FROM sys_user --",
    "admin'; EXEC xp_cmdshell('dir'); --",
    "1' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --"
)

$sqlInjectionBlocked = 0
foreach ($payload in $sqlInjectionPayloads) {
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/system/user/list?userName=$payload" -Method Get -TimeoutSec 10 -ErrorAction Stop
        if ($response.code -eq 400) {
            $sqlInjectionBlocked++
        }
    } catch {
        # 如果请求被拒绝，说明防护生效
        $sqlInjectionBlocked++
    }
}

$sqlProtectionWorking = $sqlInjectionBlocked -eq $sqlInjectionPayloads.Count
Record-TestResult "SQL Injection Protection" $sqlProtectionWorking "Blocked $sqlInjectionBlocked out of $($sqlInjectionPayloads.Count) SQL injection attempts"

# Test 3: XSS Protection
Write-Host ""
Write-Host "3. Testing XSS Protection..." -ForegroundColor Yellow

$xssPayloads = @(
    "<script>alert('xss')</script>",
    "<img src=x onerror=alert('xss')>",
    "javascript:alert('xss')",
    "<iframe src='javascript:alert(1)'></iframe>",
    "<svg onload=alert('xss')>"
)

$xssBlocked = 0
foreach ($payload in $xssPayloads) {
    try {
        $body = @{ userName = $payload } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "$baseUrl/system/user" -Method Post -Body $body -ContentType "application/json" -TimeoutSec 10 -ErrorAction Stop
        if ($response.code -eq 400) {
            $xssBlocked++
        }
    } catch {
        # 如果请求被拒绝，说明防护生效
        $xssBlocked++
    }
}

$xssProtectionWorking = $xssBlocked -eq $xssPayloads.Count
Record-TestResult "XSS Protection" $xssProtectionWorking "Blocked $xssBlocked out of $($xssPayloads.Count) XSS attempts"

# Test 4: Rate Limiting
Write-Host ""
Write-Host "4. Testing Rate Limiting..." -ForegroundColor Yellow

$rateLimitHit = $false
$requestCount = 0
$maxRequests = 150 # 超过API限流阈值

for ($i = 1; $i -le $maxRequests; $i++) {
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method Get -TimeoutSec 5 -ErrorAction Stop
        $requestCount++
        if ($response.code -eq 429) {
            $rateLimitHit = $true
            break
        }
    } catch {
        $errorResponse = $_.Exception.Response
        if ($errorResponse -and $errorResponse.StatusCode -eq 429) {
            $rateLimitHit = $true
            break
        }
    }
    
    # 短暂延迟避免过快请求
    Start-Sleep -Milliseconds 10
}

Record-TestResult "Rate Limiting" $rateLimitHit "Rate limiting triggered after $requestCount requests"

# Test 5: Login Rate Limiting
Write-Host ""
Write-Host "5. Testing Login Rate Limiting..." -ForegroundColor Yellow

$loginRateLimitHit = $false
$loginAttempts = 0
$maxLoginAttempts = 20 # 超过登录限流阈值

for ($i = 1; $i -le $maxLoginAttempts; $i++) {
    try {
        $loginBody = '{"username":"testuser","password":"wrongpassword"}'
        $response = Invoke-RestMethod -Uri "$baseUrl/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 5 -ErrorAction Stop
        $loginAttempts++
        if ($response.code -eq 429) {
            $loginRateLimitHit = $true
            break
        }
    } catch {
        $errorResponse = $_.Exception.Response
        if ($errorResponse -and $errorResponse.StatusCode -eq 429) {
            $loginRateLimitHit = $true
            break
        }
    }
    
    # 短暂延迟
    Start-Sleep -Milliseconds 50
}

Record-TestResult "Login Rate Limiting" $loginRateLimitHit "Login rate limiting triggered after $loginAttempts attempts"

# Test 6: Request Size Limiting
Write-Host ""
Write-Host "6. Testing Request Size Limiting..." -ForegroundColor Yellow

try {
    # 创建一个大的请求体（超过10MB）
    $largeData = "x" * (11 * 1024 * 1024) # 11MB
    $largeBody = @{ data = $largeData } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$baseUrl/system/user" -Method Post -Body $largeBody -ContentType "application/json" -TimeoutSec 10 -ErrorAction Stop
    Record-TestResult "Request Size Limiting" $false "Large request was not blocked"
} catch {
    $errorResponse = $_.Exception.Response
    if ($errorResponse -and $errorResponse.StatusCode -eq 413) {
        Record-TestResult "Request Size Limiting" $true "Large request properly blocked"
    } else {
        Record-TestResult "Request Size Limiting" $true "Large request blocked (connection error expected)"
    }
}

# Test 7: User Agent Filtering
Write-Host ""
Write-Host "7. Testing User Agent Filtering..." -ForegroundColor Yellow

$suspiciousUserAgents = @(
    "sqlmap/1.0",
    "Nikto/2.1.6",
    "w3af.org",
    "Acunetix",
    "Netsparker"
)

$userAgentBlocked = 0
foreach ($userAgent in $suspiciousUserAgents) {
    try {
        $headers = @{ "User-Agent" = $userAgent }
        $response = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method Get -Headers $headers -TimeoutSec 10 -ErrorAction Stop
        # 如果请求成功，说明没有被阻止
    } catch {
        $errorResponse = $_.Exception.Response
        if ($errorResponse -and $errorResponse.StatusCode -eq 403) {
            $userAgentBlocked++
        }
    }
}

$userAgentFilterWorking = $userAgentBlocked -gt 0
Record-TestResult "User Agent Filtering" $userAgentFilterWorking "Blocked $userAgentBlocked out of $($suspiciousUserAgents.Count) suspicious user agents"

# Test 8: CSRF Protection
Write-Host ""
Write-Host "8. Testing CSRF Protection..." -ForegroundColor Yellow

try {
    # 模拟跨站请求（没有正确的Origin/Referer头）
    $headers = @{ 
        "Origin" = "http://malicious-site.com"
        "Content-Type" = "application/json"
    }
    $body = '{"userName":"testuser","password":"123456"}'
    
    $response = Invoke-RestMethod -Uri "$baseUrl/system/user" -Method Post -Body $body -Headers $headers -TimeoutSec 10 -ErrorAction Stop
    Record-TestResult "CSRF Protection" $false "CSRF request was not blocked"
} catch {
    $errorResponse = $_.Exception.Response
    if ($errorResponse -and $errorResponse.StatusCode -eq 403) {
        Record-TestResult "CSRF Protection" $true "CSRF request properly blocked"
    } else {
        Record-TestResult "CSRF Protection" $false "CSRF test failed with unexpected error"
    }
}

# Test 9: Authentication Bypass Attempts
Write-Host ""
Write-Host "9. Testing Authentication Bypass Protection..." -ForegroundColor Yellow

$bypassAttempts = @(
    @{ header = "Authorization"; value = "Bearer invalid-token" },
    @{ header = "Authorization"; value = "Bearer " },
    @{ header = "Authorization"; value = "Basic YWRtaW46YWRtaW4=" },
    @{ header = "X-User-ID"; value = "1" },
    @{ header = "X-Admin"; value = "true" }
)

$bypassBlocked = 0
foreach ($attempt in $bypassAttempts) {
    try {
        $headers = @{ $attempt.header = $attempt.value }
        $response = Invoke-RestMethod -Uri "$baseUrl/system/user/list" -Method Get -Headers $headers -TimeoutSec 10 -ErrorAction Stop
        if ($response.code -eq 401 -or $response.code -eq 403) {
            $bypassBlocked++
        }
    } catch {
        $errorResponse = $_.Exception.Response
        if ($errorResponse -and ($errorResponse.StatusCode -eq 401 -or $errorResponse.StatusCode -eq 403)) {
            $bypassBlocked++
        }
    }
}

$authBypassProtected = $bypassBlocked -eq $bypassAttempts.Count
Record-TestResult "Authentication Bypass Protection" $authBypassProtected "Blocked $bypassBlocked out of $($bypassAttempts.Count) bypass attempts"

# Test 10: Input Validation
Write-Host ""
Write-Host "10. Testing Input Validation..." -ForegroundColor Yellow

$invalidInputs = @(
    @{ field = "email"; value = "invalid-email" },
    @{ field = "phone"; value = "abc123" },
    @{ field = "status"; value = "999" },
    @{ field = "userName"; value = "" },
    @{ field = "userName"; value = "a" * 100 } # 过长的用户名
)

$validationWorking = 0
foreach ($input in $invalidInputs) {
    try {
        $body = @{ $input.field = $input.value } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "$baseUrl/system/user" -Method Post -Body $body -ContentType "application/json" -TimeoutSec 10 -ErrorAction Stop
        if ($response.code -eq 400 -or $response.code -eq 500) {
            $validationWorking++
        }
    } catch {
        # 验证失败也算正常
        $validationWorking++
    }
}

$inputValidationWorking = $validationWorking -gt 0
Record-TestResult "Input Validation" $inputValidationWorking "Validated $validationWorking out of $($invalidInputs.Count) invalid inputs"

Write-Host ""
Write-Host "=== Security Hardening Test Summary ===" -ForegroundColor Blue
Write-Host "Total Tests: $($testResults.Total)" -ForegroundColor White
Write-Host "Passed: $($testResults.Passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.Failed)" -ForegroundColor Red
Write-Host "Security Score: $([math]::Round(($testResults.Passed / $testResults.Total) * 100, 2))%" -ForegroundColor Yellow

if ($testResults.Failed -gt 0) {
    Write-Host ""
    Write-Host "Failed Security Tests:" -ForegroundColor Red
    $testResults.Details | Where-Object { -not $_.Success } | ForEach-Object {
        Write-Host "  - $($_.Name): $($_.Message)" -ForegroundColor Red
    }
}

Write-Host ""
if ($testResults.Failed -eq 0) {
    Write-Host "🛡️  All security tests passed! System is well protected." -ForegroundColor Green
} elseif ($testResults.Passed / $testResults.Total -gt 0.8) {
    Write-Host "🔒 Most security tests passed. System has good protection." -ForegroundColor Yellow
} else {
    Write-Host "⚠️  Several security tests failed. Please review security configuration." -ForegroundColor Red
}

Write-Host ""
Write-Host "Security hardening test completed!" -ForegroundColor Green
