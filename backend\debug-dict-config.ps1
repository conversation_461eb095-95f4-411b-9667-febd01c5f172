# Debug Dictionary and Config Creation

Write-Host "=== Debug Dictionary and Config Creation ===" -ForegroundColor Blue

# Get authentication token
$loginBody = '{"username":"admin","password":"admin123"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
$token = $loginResponse.data.token
$headers = @{ Authorization = "Bearer $token" }

Write-Host "Token obtained successfully" -ForegroundColor Green

# Test dictionary type creation
Write-Host ""
Write-Host "1. Testing dictionary type creation..." -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    $dictTypeBody = @{
        dictName = "Test Dictionary $timestamp"
        dictType = "test_dict_type_$timestamp"
        status = "0"
        remark = "Test dictionary for integration testing"
    } | ConvertTo-Json
    
    Write-Host "Dictionary Type Body: $dictTypeBody" -ForegroundColor Gray
    
    $dictTypeResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type" -Method Post -Body $dictTypeBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Dictionary Type Response: $($dictTypeResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Dictionary Type Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Test system configuration creation
Write-Host ""
Write-Host "2. Testing system configuration creation..." -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    $configBody = @{
        configName = "Test Configuration $timestamp"
        configKey = "test.config.key.$timestamp"
        configValue = "test_value"
        configType = "Y"
        remark = "Test configuration for integration testing"
    } | ConvertTo-Json
    
    Write-Host "Config Body: $configBody" -ForegroundColor Gray
    
    $configResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/config" -Method Post -Body $configBody -ContentType "application/json" -Headers $headers -TimeoutSec 10
    Write-Host "Config Response: $($configResponse | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Config Creation Error: $_" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug completed!" -ForegroundColor Blue
