# WOSM Go后端系统监控脚本
# 版本: 1.0
# 作者: Augment Agent
# 日期: 2025-06-13

param(
    [int]$Interval = 60,  # 监控间隔（秒）
    [switch]$Continuous = $false,  # 持续监控
    [string]$AlertEmail = "",  # 告警邮箱
    [string]$LogPath = "system-monitor.log"  # 监控日志路径
)

# 配置变量
$ServiceName = "wosm"
$ServiceUrl = "http://localhost:8080"

# 告警阈值
$Thresholds = @{
    CPUPercent = 80
    MemoryMB = 1024
    ResponseTimeMs = 2000
    DiskSpacePercent = 20
}

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
    Add-Content -Path $LogPath -Value "[$timestamp] $Message"
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# 发送告警
function Send-Alert {
    param([string]$Subject, [string]$Body)
    
    Write-Warning "ALERT: $Subject"
    Write-Warning $Body
    
    if ($AlertEmail) {
        try {
            Write-Info "告警已发送到: $AlertEmail"
        } catch {
            Write-Error "发送告警邮件失败: $_"
        }
    }
}

# 检查服务进程
function Test-ServiceProcess {
    Write-Info "检查服务进程..."
    
    $process = Get-Process -Name $ServiceName -ErrorAction SilentlyContinue
    if ($process) {
        $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        
        Write-Success "服务进程运行正常"
        Write-Info "  进程ID: $($process.Id)"
        Write-Info "  内存使用: ${memoryMB}MB"
        
        # 检查内存告警
        if ($memoryMB -gt $Thresholds.MemoryMB) {
            Send-Alert "内存使用过高" "当前内存使用: ${memoryMB}MB，阈值: $($Thresholds.MemoryMB)MB"
        }
        
        return @{
            Status = "Running"
            ProcessId = $process.Id
            MemoryMB = $memoryMB
        }
    } else {
        Write-Error "服务进程未运行"
        Send-Alert "服务进程异常" "WOSM服务进程未运行，请检查服务状态"
        
        return @{
            Status = "Stopped"
            ProcessId = $null
            MemoryMB = 0
        }
    }
}

# 检查服务健康状态
function Test-ServiceHealth {
    Write-Info "检查服务健康状态..."
    
    try {
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "$ServiceUrl/health" -TimeoutSec 10
        $endTime = Get-Date
        $responseTime = ($endTime - $startTime).TotalMilliseconds
        
        if ($response.status -eq "up") {
            Write-Success "服务健康检查通过"
            Write-Info "  响应时间: ${responseTime}ms"
            Write-Info "  服务版本: $($response.version)"
            
            # 检查响应时间告警
            if ($responseTime -gt $Thresholds.ResponseTimeMs) {
                Send-Alert "响应时间过长" "当前响应时间: ${responseTime}ms，阈值: $($Thresholds.ResponseTimeMs)ms"
            }
            
            return @{
                Status = "Healthy"
                ResponseTime = $responseTime
                Version = $response.version
            }
        } else {
            Write-Warning "服务健康检查失败: $($response.status)"
            Send-Alert "服务健康检查失败" "服务返回状态: $($response.status)"
            
            return @{
                Status = "Unhealthy"
                ResponseTime = $responseTime
                Version = $null
            }
        }
    } catch {
        Write-Error "服务健康检查异常: $_"
        Send-Alert "服务健康检查异常" "无法连接到服务: $_"
        
        return @{
            Status = "Error"
            ResponseTime = $null
            Version = $null
        }
    }
}

# 检查数据库连接
function Test-DatabaseConnection {
    Write-Info "检查数据库连接..."
    
    try {
        $result = sqlcmd -S localhost -d wosm -U sa -P "F@2233" -Q "SELECT 1" -h -1 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库连接正常"
            return @{ Status = "Connected" }
        } else {
            Write-Error "数据库连接失败"
            Send-Alert "数据库连接失败" "无法连接到数据库服务器"
            return @{ Status = "Disconnected" }
        }
    } catch {
        Write-Error "数据库连接检查异常: $_"
        Send-Alert "数据库连接异常" "数据库连接检查出现异常: $_"
        return @{ Status = "Error" }
    }
}

# 检查磁盘空间
function Test-DiskSpace {
    Write-Info "检查磁盘空间..."
    
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    foreach ($drive in $drives) {
        $freeSpacePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        
        Write-Info "  驱动器 $($drive.DeviceID) - 可用空间: ${freeSpaceGB}GB / ${totalSpaceGB}GB ($freeSpacePercent%)"
        
        if ($freeSpacePercent -lt $Thresholds.DiskSpacePercent) {
            Send-Alert "磁盘空间不足" "驱动器 $($drive.DeviceID) 可用空间仅剩 $freeSpacePercent%"
        }
    }
    
    return $drives | ForEach-Object {
        @{
            Drive = $_.DeviceID
            FreeSpacePercent = [math]::Round(($_.FreeSpace / $_.Size) * 100, 2)
            FreeSpaceGB = [math]::Round($_.FreeSpace / 1GB, 2)
            TotalSpaceGB = [math]::Round($_.Size / 1GB, 2)
        }
    }
}

# 检查系统资源
function Test-SystemResources {
    Write-Info "检查系统资源..."
    
    # 内存使用率
    $totalMemory = (Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory
    $availableMemory = (Get-Counter "\Memory\Available MBytes").CounterSamples[0].CookedValue * 1MB
    $usedMemoryPercent = [math]::Round((($totalMemory - $availableMemory) / $totalMemory) * 100, 2)
    
    Write-Info "  系统内存使用率: $usedMemoryPercent%"
    
    return @{
        MemoryUsage = $usedMemoryPercent
        TotalMemoryGB = [math]::Round($totalMemory / 1GB, 2)
        AvailableMemoryGB = [math]::Round($availableMemory / 1GB, 2)
    }
}

# 生成监控报告
function Generate-MonitoringReport {
    param($Results)
    
    Write-Info "=== 监控报告 ==="
    Write-Info "监控时间: $(Get-Date)"
    Write-Info ""
    
    Write-Info "服务状态:"
    Write-Info "  进程状态: $($Results.Process.Status)"
    Write-Info "  健康状态: $($Results.Health.Status)"
    Write-Info "  数据库状态: $($Results.Database.Status)"
    Write-Info ""
    
    if ($Results.Process.Status -eq "Running") {
        Write-Info "性能指标:"
        Write-Info "  内存使用: $($Results.Process.MemoryMB)MB"
        if ($Results.Health.ResponseTime) {
            Write-Info "  响应时间: $($Results.Health.ResponseTime)ms"
        }
    }
    
    Write-Info ""
    Write-Info "系统资源:"
    Write-Info "  系统内存: $($Results.System.MemoryUsage)%"
    
    Write-Info ""
    Write-Info "磁盘空间:"
    foreach ($disk in $Results.Disk) {
        Write-Info "  $($disk.Drive) $($disk.FreeSpacePercent)% 可用"
    }
    
    Write-Info "=== 报告结束 ==="
}

# 主监控函数
function Start-Monitoring {
    do {
        Write-Info "开始系统监控检查..."
        
        $results = @{
            Process = Test-ServiceProcess
            Health = Test-ServiceHealth
            Database = Test-DatabaseConnection
            Disk = Test-DiskSpace
            System = Test-SystemResources
        }
        
        Generate-MonitoringReport $results
        
        if ($Continuous) {
            Write-Info "等待 $Interval 秒后进行下次检查..."
            Start-Sleep -Seconds $Interval
        }
        
    } while ($Continuous)
}

# 脚本入口
Write-Info "=== WOSM Go后端系统监控启动 ==="
Write-Info "监控间隔: $Interval 秒"
Write-Info "持续监控: $Continuous"
Write-Info "日志文件: $LogPath"

if ($AlertEmail) {
    Write-Info "告警邮箱: $AlertEmail"
}

Write-Info ""

# 开始监控
Start-Monitoring

Write-Info "监控脚本执行完成"
