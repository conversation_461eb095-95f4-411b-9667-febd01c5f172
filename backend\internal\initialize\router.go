package initialize

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/filter"
	"github.com/ruoyi/backend/internal/middleware"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/router"
	"go.uber.org/zap"
)

// 全局验证码存储（生产环境应该使用Redis）
var captchaStore = make(map[string]string)

// InitRouter 初始化路由
func InitRouter(mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) *gin.Engine {
	// 设置运行模式
	appConfig := config.NewAppConfig() // 创建AppConfig实例
	if appConfig.Env == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else if appConfig.Env == "test" {
		gin.SetMode(gin.TestMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 创建路由引擎
	engine := gin.New()

	// 注册安全中间件
	engine.Use(middleware.SecurityHeadersMiddleware())
	engine.Use(middleware.RequestSizeMiddleware(10 * 1024 * 1024)) // 10MB限制
	engine.Use(middleware.UserAgentFilterMiddleware())
	engine.Use(middleware.SQLInjectionMiddleware(log))
	engine.Use(middleware.XSSProtectionMiddleware(log))
	engine.Use(middleware.CSRFProtectionMiddleware(log))
	engine.Use(middleware.APIRateLimitMiddleware(log))

	// 注册基础中间件
	engine.Use(middleware.ZapLogger(log))
	engine.Use(middleware.RecoveryMiddleware(log)) // 使用自定义的Recovery中间件
	engine.Use(gzip.Gzip(gzip.DefaultCompression))

	// 注册可重复请求过滤器
	repeatableFilter := filter.NewRepeatableFilter(log)
	engine.Use(repeatableFilter.Filter())

	// 注册XSS过滤中间件
	engine.Use(middleware.XssMiddleware(log))

	// 配置CORS
	if mainConfig.Resources.IsCorsEnabled() {
		corsConfig := cors.Config{
			AllowOrigins:     mainConfig.Resources.GetCorsAllowedOrigins(),
			AllowMethods:     mainConfig.Resources.GetCorsAllowedMethods(),
			AllowHeaders:     mainConfig.Resources.GetCorsAllowedHeaders(),
			AllowCredentials: mainConfig.Resources.IsCorsAllowCredentials(),
			MaxAge:           time.Duration(mainConfig.Resources.GetCorsMaxAge()) * time.Second,
		}
		engine.Use(cors.New(corsConfig))
	}

	// 注册性能分析工具（仅在开发环境下）
	if appConfig.Env == "dev" {
		pprof.Register(engine)
	}

	// 注册静态资源
	engine.Static("/static", "./static")
	engine.StaticFile("/favicon.ico", "./static/favicon.ico")

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "up",
			"service": appConfig.Name,
			"version": appConfig.Version,
		})
	})

	// 注册前端兼容路由（直接访问，不带/api前缀）
	registerFrontendCompatibilityRoutes(engine, mainConfig, log, redisService, redisCache)

	// 注册API路由
	apiGroup := engine.Group("/api")
	{
		// 启用完整系统路由 - 企业级功能
		router.RegisterSystemRoutes(apiGroup, log, DB)

		// 保留简化路由作为备用
		simpleGroup := apiGroup.Group("/simple")
		{
			router.RegisterSimpleSystemRoutes(simpleGroup, log, DB)
		}

		// 保留演示路由作为备用
		demoGroup := apiGroup.Group("/demo")
		{
			// 无需认证的演示路由
			publicGroup := demoGroup.Group("/public")
			registerPublicRoutes(publicGroup, mainConfig, log, redisService, redisCache)

			// 需要认证的演示路由
			privateGroup := demoGroup.Group("/")
			privateGroup.Use(middleware.JWTAuth())
			privateGroup.Use(middleware.RepeatSubmitMiddleware(log, redisService))
			registerPrivateRoutes(privateGroup, mainConfig, log, redisService, redisCache)
		}
	}

	// 注册404处理
	engine.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "Not Found",
		})
	})

	return engine
}

// registerPublicRoutes 注册公开路由
func registerPublicRoutes(group *gin.RouterGroup, mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) {
	// 登录接口 - 添加登录限流
	group.POST("/login", middleware.LoginRateLimitMiddleware(log), func(c *gin.Context) {
		// 生成真实的JWT令牌
		token, err := middleware.GenerateToken(1, "admin")
		if err != nil {
			log.Error("生成JWT令牌失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "生成令牌失败",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "登录成功",
			"data": gin.H{
				"token": token,
				"user": gin.H{
					"userId":    1,
					"userName":  "管理员",
					"loginName": "admin",
					"email":     "<EMAIL>",
				},
			},
		})
	})

	// 验证码接口
	group.GET("/captcha", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "验证码接口开发中",
			"data": gin.H{
				"uuid": "captcha-uuid-12345",
				"img":  "data:image/png;base64,demo-captcha-image",
			},
		})
	})
}

// registerPrivateRoutes 注册私有路由
func registerPrivateRoutes(group *gin.RouterGroup, mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) {
	// 获取用户信息
	group.GET("/getInfo", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": gin.H{
				"user": gin.H{
					"userId":    1,
					"userName":  "管理员",
					"loginName": "admin",
					"email":     "<EMAIL>",
					"status":    "0",
				},
				"roles":       []string{"admin"},
				"permissions": []string{"*:*:*"},
			},
		})
	})

	// 获取路由信息
	group.GET("/getRouters", func(c *gin.Context) {
		menus := []gin.H{
			{
				"name":      "System",
				"path":      "/system",
				"component": "Layout",
				"meta":      gin.H{"title": "系统管理", "icon": "system"},
				"children": []gin.H{
					{
						"name":      "User",
						"path":      "/system/user",
						"component": "system/user/index",
						"meta":      gin.H{"title": "用户管理", "icon": "user"},
					},
				},
			},
		}
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": menus,
		})
	})

	// 系统管理路由组
	systemGroup := group.Group("/system")
	{
		// 用户管理
		systemGroup.GET("/user/list", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"list": []gin.H{
						{
							"userId":     1,
							"userName":   "管理员",
							"loginName":  "admin",
							"email":      "<EMAIL>",
							"status":     "0",
							"createTime": "2024-01-01 00:00:00",
						},
					},
					"total": 1,
					"page":  1,
					"size":  10,
				},
			})
		})
	}
}

// registerFrontendCompatibilityRoutes 注册前端兼容路由
func registerFrontendCompatibilityRoutes(engine *gin.Engine, mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) {
	// 登录接口 - 前端直接访问 /login
	engine.POST("/login", middleware.LoginRateLimitMiddleware(log), func(c *gin.Context) {
		// 解析登录请求
		var loginReq struct {
			Username string `json:"username"`
			Password string `json:"password"`
			Code     string `json:"code"`
			Uuid     string `json:"uuid"`
		}

		if err := c.ShouldBindJSON(&loginReq); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code": 400,
				"msg":  "请求参数错误",
			})
			return
		}

		// 验证码验证（如果提供了验证码）
		if loginReq.Code != "" && loginReq.Uuid != "" {
			// 检查验证码是否正确
			if expectedAnswer, exists := captchaStore[loginReq.Uuid]; exists {
				if expectedAnswer != loginReq.Code {
					c.JSON(http.StatusOK, gin.H{
						"code": 500,
						"msg":  "验证码错误",
					})
					return
				}
				// 验证成功后删除验证码
				delete(captchaStore, loginReq.Uuid)
			} else {
				c.JSON(http.StatusOK, gin.H{
					"code": 500,
					"msg":  "验证码已失效",
				})
				return
			}
		}

		// 简单验证（生产环境应该验证数据库）
		if loginReq.Username != "admin" || loginReq.Password != "admin123" {
			c.JSON(http.StatusOK, gin.H{
				"code": 500,
				"msg":  "用户名或密码错误",
			})
			return
		}

		// 生成真实的JWT令牌
		token, err := middleware.GenerateToken(1, "admin")
		if err != nil {
			log.Error("生成JWT令牌失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "生成令牌失败",
			})
			return
		}

		// 尝试多种前端可能期望的响应格式
		c.JSON(http.StatusOK, gin.H{
			"code":  200,
			"msg":   "登录成功",
			"token": token, // 直接在根级别提供token
			"data": gin.H{
				"token": token, // 同时在data中也提供token
				"user": gin.H{
					"userId":    1,
					"userName":  "管理员",
					"loginName": "admin",
					"email":     "<EMAIL>",
				},
			},
		})
	})

	// 验证码接口 - 前端直接访问 /captchaImage
	engine.GET("/captchaImage", func(c *gin.Context) {
		// 生成UUID
		uuid := fmt.Sprintf("captcha-%d", time.Now().UnixNano())

		// 使用base64Captcha库生成GIF格式验证码
		driver := base64Captcha.NewDriverString(80, 240, 0, 0, 4, "1234567890", nil, nil, []string{})
		captcha := base64Captcha.NewCaptcha(driver, base64Captcha.DefaultMemStore)

		// 生成验证码
		id, b64s, answer, err := captcha.Generate()
		if err != nil {
			log.Error("生成验证码失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "生成验证码失败",
			})
			return
		}

		// 移除data:image前缀，因为前端会自己添加data:image/gif;base64,前缀
		imgBase64 := b64s
		if strings.HasPrefix(b64s, "data:image/png;base64,") {
			imgBase64 = b64s[22:] // 移除"data:image/png;base64,"前缀
		} else if strings.HasPrefix(b64s, "data:image/gif;base64,") {
			imgBase64 = b64s[22:] // 移除"data:image/gif;base64,"前缀
		} else if strings.Contains(b64s, ";base64,") {
			// 处理其他格式的data URL
			parts := strings.Split(b64s, ";base64,")
			if len(parts) == 2 {
				imgBase64 = parts[1]
			}
		}

		// 存储验证码答案，用于后续验证
		// 这里简单存储在内存中，实际项目中应该存储在Redis或数据库中
		captchaStore[uuid] = answer

		log.Info("生成验证码成功", zap.String("uuid", uuid), zap.String("id", id), zap.String("answer", answer))

		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"uuid": uuid,
			"img":  imgBase64, // 返回不包含前缀的base64字符串，前端会添加前缀
		})
	})

	// 获取用户信息 - 前端直接访问 /getInfo
	engine.GET("/getInfo", func(c *gin.Context) {
		// 检查Authorization头
		authHeader := c.GetHeader("Authorization")
		log.Info("收到getInfo请求", zap.String("Authorization", authHeader))

		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "未提供认证令牌",
			})
			return
		}

		// 移除Bearer前缀
		token := authHeader
		if strings.HasPrefix(authHeader, "Bearer ") {
			token = authHeader[7:]
		}

		// 验证JWT令牌
		claims, err := middleware.ParseToken(token)
		if err != nil {
			log.Error("JWT令牌验证失败", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "无效的认证令牌: " + err.Error(),
			})
			return
		}

		log.Info("JWT令牌验证成功", zap.Int64("userId", claims.UserID), zap.String("username", claims.Username))

		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": gin.H{
				"user": gin.H{
					"userId":    claims.UserID,
					"userName":  "管理员",
					"nickName":  "管理员", // 添加nickName字段
					"loginName": claims.Username,
					"email":     "<EMAIL>",
					"status":    "0",
					"avatar":    "", // 添加avatar字段
				},
				"roles":       []string{"admin"},
				"permissions": []string{"*:*:*"},
			},
		})
	})

	// 获取路由信息 - 前端直接访问 /getRouters
	engine.GET("/getRouters", middleware.JWTAuth(), func(c *gin.Context) {
		menus := []gin.H{
			{
				"name":      "System",
				"path":      "/system",
				"component": "Layout",
				"meta":      gin.H{"title": "系统管理", "icon": "system"},
				"children": []gin.H{
					{
						"name":      "User",
						"path":      "/system/user",
						"component": "system/user/index",
						"meta":      gin.H{"title": "用户管理", "icon": "user"},
					},
				},
			},
		}
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": menus,
		})
	})

	// 登出接口 - 前端直接访问 /logout
	engine.POST("/logout", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "退出成功",
		})
	})
}
