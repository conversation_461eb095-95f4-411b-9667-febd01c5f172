# WOSM Service Status Check

Write-Host "=== WOSM 服务状态检查 ===" -ForegroundColor Blue
Write-Host ""

# 检查进程
$process = Get-Process -Name "wosm" -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "✅ 服务进程运行中" -ForegroundColor Green
    Write-Host "   PID: $($process.Id)" -ForegroundColor Gray
    Write-Host "   内存: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor Gray
    Write-Host "   启动时间: $($process.StartTime)" -ForegroundColor Gray
} else {
    Write-Host "❌ 服务进程未运行" -ForegroundColor Red
}

Write-Host ""

# 检查端口
$port = 8080
$portInfo = netstat -ano | findstr ":$port "
if ($portInfo) {
    Write-Host "✅ 端口 $port 已监听" -ForegroundColor Green
    $portInfo | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ 端口 $port 未监听" -ForegroundColor Red
}

Write-Host ""

# 健康检查
try {
    $health = Invoke-RestMethod -Uri "http://localhost:$port/health" -Method Get -TimeoutSec 5
    Write-Host "✅ 健康检查通过" -ForegroundColor Green
    Write-Host "   服务: $($health.service)" -ForegroundColor Gray
    Write-Host "   版本: $($health.version)" -ForegroundColor Gray
    Write-Host "   状态: $($health.status)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 健康检查失败" -ForegroundColor Red
    Write-Host "   错误: $_" -ForegroundColor Gray
}

Write-Host ""

# API测试
try {
    Write-Host "🔍 测试登录API..." -ForegroundColor Yellow
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:$port/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ 登录API正常" -ForegroundColor Green
        
        # 测试用户列表API
        $token = $loginResponse.data.token
        $headers = @{ Authorization = "Bearer $token" }
        $userList = Invoke-RestMethod -Uri "http://localhost:$port/api/system/user/list" -Headers $headers -TimeoutSec 10
        
        if ($userList.code -eq 200) {
            Write-Host "✅ 用户列表API正常" -ForegroundColor Green
            Write-Host "   用户总数: $($userList.data.total)" -ForegroundColor Gray
        } else {
            Write-Host "❌ 用户列表API异常" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 登录API异常" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ API测试失败" -ForegroundColor Red
    Write-Host "   错误: $_" -ForegroundColor Gray
}

Write-Host ""
Write-Host "=== 检查完成 ===" -ForegroundColor Blue
