package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysDeptController 部门信息控制器
type SysDeptController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysDeptController 创建部门控制器
func NewSysDeptController(logger *zap.Logger, db *gorm.DB) *SysDeptController {
	return &SysDeptController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysDeptController的路由
func (c *SysDeptController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                         // 部门列表
	r.GET("/list/exclude/:deptId", c.ExcludeChild) // 排除节点的部门列表
	r.GET("/:deptId", c.GetInfo)                   // 获取部门详情
	r.POST("", c.Add)                              // 新增部门
	r.PUT("", c.Edit)                              // 修改部门
	r.DELETE("/:deptId", c.Remove)                 // 删除部门
}

// List 获取部门列表 - 完全按照Java后端业务逻辑
func (c *SysDeptController) List(ctx *gin.Context) {
	var depts []domain.SysDept

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysDept{}).Where("del_flag = ?", "0")

	// 部门名称模糊查询
	if deptName := ctx.Query("deptName"); deptName != "" {
		query = query.Where("dept_name LIKE ?", "%"+deptName+"%")
	}

	// 部门状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 数据权限控制 - 根据用户权限过滤部门
	// TODO: 实现数据权限过滤逻辑

	// 排序 - 按父部门ID和显示顺序排序
	query = query.Order("parent_id ASC, order_num ASC")

	// 执行查询
	if err := query.Find(&depts).Error; err != nil {
		c.Logger.Error("查询部门列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 构建树形结构
	deptTree := c.buildDeptTree(depts, 0)

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, deptTree)
}

// ExcludeChild 查询部门列表（排除节点） - 完全按照Java后端逻辑
func (c *SysDeptController) ExcludeChild(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	var depts []domain.SysDept

	// 查询所有部门
	query := c.db.Model(&domain.SysDept{}).Where("del_flag = ?", "0")
	query = query.Order("parent_id ASC, order_num ASC")

	if err := query.Find(&depts).Error; err != nil {
		c.Logger.Error("查询部门列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 排除指定节点及其子节点 - 与Java后端逻辑一致
	var filteredDepts []domain.SysDept
	for _, dept := range depts {
		// 排除自己
		if dept.DeptId == deptId {
			continue
		}

		// 排除子节点（通过ancestors字段判断）
		if dept.Ancestors != "" {
			ancestorIds := strings.Split(dept.Ancestors, ",")
			isChild := false
			for _, ancestorId := range ancestorIds {
				if ancestorId == deptIdStr {
					isChild = true
					break
				}
			}
			if isChild {
				continue
			}
		}

		filteredDepts = append(filteredDepts, dept)
	}

	// 构建树形结构
	deptTree := c.buildDeptTree(filteredDepts, 0)

	c.SuccessWithData(ctx, deptTree)
}

// GetInfo 根据部门编号获取详细信息 - 完全按照Java后端逻辑
func (c *SysDeptController) GetInfo(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	// 数据权限检查
	// TODO: 实现checkDeptDataScope逻辑

	var dept domain.SysDept
	if err := c.db.Where("dept_id = ? AND del_flag = ?", deptId, "0").First(&dept).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "部门不存在")
		} else {
			c.Logger.Error("查询部门信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	c.SuccessWithData(ctx, dept)
}

// Add 新增部门 - 完全按照Java后端业务逻辑
func (c *SysDeptController) Add(ctx *gin.Context) {
	var dept domain.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if dept.DeptName == "" {
		c.ErrorWithMessage(ctx, "部门名称不能为空")
		return
	}
	if dept.OrderNum == 0 {
		c.ErrorWithMessage(ctx, "显示顺序不能为空")
		return
	}

	// 检查部门名称唯一性
	if !c.checkDeptNameUnique(&dept) {
		c.Logger.Error("部门名称唯一性检查失败", zap.String("deptName", dept.DeptName), zap.Int64("parentId", dept.ParentId))
		c.ErrorWithMessage(ctx, "新增部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 设置默认值
	dept.CreateBy = c.GetUsername(ctx)
	if dept.Status == "" {
		dept.Status = "0" // 默认正常状态
	}
	if dept.DelFlag == "" {
		dept.DelFlag = "0" // 默认未删除
	}

	// 设置祖级列表
	if dept.ParentId == 0 {
		dept.Ancestors = "0"
	} else {
		// 获取父部门信息
		var parentDept domain.SysDept
		if err := c.db.Where("dept_id = ? AND del_flag = ?", dept.ParentId, "0").First(&parentDept).Error; err != nil {
			c.ErrorWithMessage(ctx, "父部门不存在")
			return
		}
		dept.Ancestors = parentDept.Ancestors + "," + strconv.FormatInt(dept.ParentId, 10)
	}

	// 保存部门
	if err := c.db.Create(&dept).Error; err != nil {
		c.Logger.Error("新增部门失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改部门 - 完全按照Java后端业务逻辑
func (c *SysDeptController) Edit(ctx *gin.Context) {
	var dept domain.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if dept.DeptId == 0 {
		c.ErrorWithMessage(ctx, "部门ID不能为空")
		return
	}
	if dept.DeptName == "" {
		c.ErrorWithMessage(ctx, "部门名称不能为空")
		return
	}

	// 数据权限检查
	// TODO: 实现checkDeptDataScope逻辑

	// 检查部门是否存在
	var existingDept domain.SysDept
	if err := c.db.Where("dept_id = ? AND del_flag = ?", dept.DeptId, "0").First(&existingDept).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "部门不存在")
		} else {
			c.Logger.Error("查询部门失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查部门名称唯一性（排除自己）
	if !c.checkDeptNameUnique(&dept) {
		c.ErrorWithMessage(ctx, "修改部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 检查上级部门不能选择自己
	if dept.DeptId == dept.ParentId {
		c.ErrorWithMessage(ctx, "修改部门'"+dept.DeptName+"'失败，上级部门不能是自己")
		return
	}

	// 检查部门状态 - 如果停用部门，不能有正常状态的子部门
	if dept.Status == "1" && c.selectNormalChildrenDeptById(dept.DeptId) > 0 {
		c.ErrorWithMessage(ctx, "该部门包含未停用的子部门！")
		return
	}

	// 设置更新者
	dept.UpdateBy = c.GetUsername(ctx)

	// 更新祖级列表（如果父部门发生变化）
	if dept.ParentId != existingDept.ParentId {
		if dept.ParentId == 0 {
			dept.Ancestors = "0"
		} else {
			// 获取父部门信息
			var parentDept domain.SysDept
			if err := c.db.Where("dept_id = ? AND del_flag = ?", dept.ParentId, "0").First(&parentDept).Error; err != nil {
				c.ErrorWithMessage(ctx, "父部门不存在")
				return
			}
			dept.Ancestors = parentDept.Ancestors + "," + strconv.FormatInt(dept.ParentId, 10)
		}

		// 更新所有子部门的祖级列表
		c.updateChildrenAncestors(dept.DeptId, existingDept.Ancestors, dept.Ancestors)
	}

	// 更新部门
	if err := c.db.Model(&existingDept).Updates(&dept).Error; err != nil {
		c.Logger.Error("修改部门失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除部门 - 完全按照Java后端业务逻辑
func (c *SysDeptController) Remove(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	// 检查是否存在子部门
	if c.hasChildByDeptId(deptId) {
		c.ErrorWithMessage(ctx, "存在下级部门,不允许删除")
		return
	}

	// 检查部门是否存在用户
	if c.checkDeptExistUser(deptId) {
		c.ErrorWithMessage(ctx, "部门存在用户,不允许删除")
		return
	}

	// 数据权限检查
	// TODO: 实现checkDeptDataScope逻辑

	// 检查部门是否存在
	var dept domain.SysDept
	if err := c.db.Where("dept_id = ? AND del_flag = ?", deptId, "0").First(&dept).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "部门不存在")
		} else {
			c.Logger.Error("查询部门失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 软删除部门
	if err := c.db.Model(&dept).Update("del_flag", "2").Error; err != nil {
		c.Logger.Error("删除部门失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// 辅助函数

// buildDeptTree 构建部门树形结构
func (c *SysDeptController) buildDeptTree(depts []domain.SysDept, parentId int64) []domain.SysDept {
	var tree []domain.SysDept

	for _, dept := range depts {
		if dept.ParentId == parentId {
			children := c.buildDeptTree(depts, dept.DeptId)
			dept.Children = children
			tree = append(tree, dept)
		}
	}

	return tree
}

// checkDeptNameUnique 校验部门名称是否唯一
func (c *SysDeptController) checkDeptNameUnique(dept *domain.SysDept) bool {
	var count int64
	query := c.db.Model(&domain.SysDept{}).Where("dept_name = ? AND parent_id = ? AND del_flag = ?", dept.DeptName, dept.ParentId, "0")

	// 如果是修改操作，排除自己
	if dept.DeptId > 0 {
		query = query.Where("dept_id != ?", dept.DeptId)
	}

	err := query.Count(&count).Error
	if err != nil {
		c.Logger.Error("检查部门名称唯一性失败", zap.Error(err), zap.String("deptName", dept.DeptName), zap.Int64("parentId", dept.ParentId))
		return false
	}

	c.Logger.Info("部门名称唯一性检查", zap.String("deptName", dept.DeptName), zap.Int64("parentId", dept.ParentId), zap.Int64("count", count))
	return count == 0
}

// selectNormalChildrenDeptById 查询部门正常状态子部门数量
func (c *SysDeptController) selectNormalChildrenDeptById(deptId int64) int64 {
	var count int64
	c.db.Model(&domain.SysDept{}).Where("status = ? AND del_flag = ? AND FIND_IN_SET(?, ancestors)", "0", "0", deptId).Count(&count)
	return count
}

// updateChildrenAncestors 更新子部门的祖级列表
func (c *SysDeptController) updateChildrenAncestors(deptId int64, oldAncestors, newAncestors string) {
	var childDepts []domain.SysDept
	c.db.Where("FIND_IN_SET(?, ancestors) AND del_flag = ?", deptId, "0").Find(&childDepts)

	for _, child := range childDepts {
		newChildAncestors := strings.Replace(child.Ancestors, oldAncestors, newAncestors, 1)
		c.db.Model(&child).Update("ancestors", newChildAncestors)
	}
}

// hasChildByDeptId 是否存在子部门
func (c *SysDeptController) hasChildByDeptId(deptId int64) bool {
	var count int64
	c.db.Model(&domain.SysDept{}).Where("parent_id = ? AND del_flag = ?", deptId, "0").Count(&count)
	return count > 0
}

// checkDeptExistUser 查询部门是否存在用户
func (c *SysDeptController) checkDeptExistUser(deptId int64) bool {
	// TODO: 查询sys_user表检查部门是否被用户使用
	// 暂时返回false
	return false
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysDeptController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysDeptController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysDeptController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysDeptController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}
