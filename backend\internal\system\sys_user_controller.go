package system

import (
	"crypto/md5"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ruoyi/backend/internal/domain"
)

// SysUserController 用户信息控制器
type SysUserController struct {
	Logger *zap.Logger
	db     *gorm.DB
}

// NewSysUserController 创建用户控制器
func NewSysUserController(logger *zap.Logger, db *gorm.DB) *SysUserController {
	return &SysUserController{
		Logger: logger,
		db:     db,
	}
}

// RegisterRoutes 注册路由 - 完全按照Java后端SysUserController的路由
func (c *SysUserController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)                      // 用户列表
	r.POST("/export", c.Export)                 // 导出用户
	r.POST("/importData", c.ImportData)         // 导入用户
	r.POST("/importTemplate", c.ImportTemplate) // 导入模板
	r.GET("/", c.GetInfo)                       // 获取用户信息（新增时）
	r.GET("/:userId", c.GetInfo)                // 获取用户详情
	r.POST("", c.Add)                           // 新增用户
	r.PUT("", c.Edit)                           // 修改用户
	r.DELETE("/:userIds", c.Remove)             // 删除用户
	r.PUT("/resetPwd", c.ResetPwd)              // 重置密码
	r.PUT("/changeStatus", c.ChangeStatus)      // 状态修改
	r.GET("/authRole/:userId", c.AuthRole)      // 获取授权角色
	r.PUT("/authRole", c.InsertAuthRole)        // 用户授权角色
	r.GET("/deptTree", c.DeptTree)              // 部门树列表
}

// List 获取用户列表 - 完全按照Java后端业务逻辑
func (c *SysUserController) List(ctx *gin.Context) {
	// 分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (pageNum - 1) * pageSize

	var users []domain.SysUser
	var total int64

	// 构建查询条件 - 与Java后端一致
	query := c.db.Model(&domain.SysUser{}).Where("del_flag = ?", "0")

	// 用户名模糊查询
	if userName := ctx.Query("userName"); userName != "" {
		query = query.Where("user_name LIKE ?", "%"+userName+"%")
	}

	// 手机号码查询
	if phonenumber := ctx.Query("phonenumber"); phonenumber != "" {
		query = query.Where("phonenumber LIKE ?", "%"+phonenumber+"%")
	}

	// 用户状态查询
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 部门ID查询
	if deptId := ctx.Query("deptId"); deptId != "" {
		query = query.Where("dept_id = ?", deptId)
	}

	// 时间范围查询
	if beginTime := ctx.Query("beginTime"); beginTime != "" {
		query = query.Where("create_time >= ?", beginTime)
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		query = query.Where("create_time <= ?", endTime)
	}

	// 数据权限控制 - 根据用户权限过滤用户
	// TODO: 实现数据权限过滤逻辑

	// 统计总数
	query.Count(&total)

	// 排序和分页
	query = query.Order("user_id ASC").Offset(offset).Limit(pageSize)

	// 执行查询
	if err := query.Find(&users).Error; err != nil {
		c.Logger.Error("查询用户列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 关联查询部门信息
	for i := range users {
		if users[i].DeptId > 0 {
			var dept domain.SysDept
			if err := c.db.Where("dept_id = ? AND del_flag = ?", users[i].DeptId, "0").First(&dept).Error; err == nil {
				users[i].Dept = &dept
			}
		}
	}

	// 返回结果 - 与Java后端响应格式完全一致
	c.SuccessWithData(ctx, gin.H{
		"total": total,
		"rows":  users,
	})
}

// GetInfo 根据用户编号获取详细信息 - 完全按照Java后端逻辑
func (c *SysUserController) GetInfo(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")

	result := gin.H{}

	// 如果有用户ID，查询用户详情
	if userIdStr != "" {
		userId, err := strconv.ParseInt(userIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "用户ID格式错误")
			return
		}

		// 数据权限检查
		// TODO: 实现checkUserDataScope逻辑

		var user domain.SysUser
		if err := c.db.Where("user_id = ? AND del_flag = ?", userId, "0").First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.ErrorWithMessage(ctx, "用户不存在")
			} else {
				c.Logger.Error("查询用户信息失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "查询失败")
			}
			return
		}

		// 查询用户关联的部门信息
		if user.DeptId > 0 {
			var dept domain.SysDept
			if err := c.db.Where("dept_id = ? AND del_flag = ?", user.DeptId, "0").First(&dept).Error; err == nil {
				user.Dept = &dept
			}
		}

		// 查询用户关联的角色
		var userRoles []domain.SysUserRole
		if err := c.db.Where("user_id = ?", userId).Find(&userRoles).Error; err == nil {
			var roleIds []int64
			for _, ur := range userRoles {
				roleIds = append(roleIds, ur.RoleId)
			}
			user.RoleIds = roleIds
		}

		// 查询用户关联的岗位
		var userPosts []domain.SysUserPost
		if err := c.db.Where("user_id = ?", userId).Find(&userPosts).Error; err == nil {
			var postIds []int64
			for _, up := range userPosts {
				postIds = append(postIds, up.PostId)
			}
			user.PostIds = postIds
		}

		result["data"] = user
	}

	// 查询所有角色
	var roles []domain.SysRole
	roleQuery := c.db.Model(&domain.SysRole{}).Where("del_flag = ? AND status = ?", "0", "0")

	// 如果不是管理员，过滤掉管理员角色
	// TODO: 实现isAdmin检查

	roleQuery.Find(&roles)
	result["roles"] = roles

	// 查询所有岗位
	var posts []domain.SysPost
	c.db.Model(&domain.SysPost{}).Where("del_flag = ? AND status = ?", "0", "0").Find(&posts)
	result["posts"] = posts

	c.SuccessWithData(ctx, result)
}

// Add 新增用户 - 完全按照Java后端业务逻辑
func (c *SysUserController) Add(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证 - 与Java后端一致
	if user.UserName == "" {
		c.ErrorWithMessage(ctx, "用户账号不能为空")
		return
	}
	if user.Password == "" {
		c.ErrorWithMessage(ctx, "用户密码不能为空")
		return
	}

	// 数据权限检查
	// TODO: 实现checkDeptDataScope和checkRoleDataScope逻辑

	// 检查用户名唯一性
	if !c.checkUserNameUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 检查手机号唯一性
	if user.Phonenumber != "" && !c.checkPhoneUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 检查邮箱唯一性
	if user.Email != "" && !c.checkEmailUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置默认值
	user.CreateBy = c.GetUsername(ctx)
	if user.Status == "" {
		user.Status = "0" // 默认正常状态
	}
	if user.DelFlag == "" {
		user.DelFlag = "0" // 默认未删除
	}

	// 密码加密
	user.Password = c.encryptPassword(user.Password)

	// 开启事务
	tx := c.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 保存用户
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("新增用户失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	// 保存用户角色关联
	if len(user.RoleIds) > 0 {
		for _, roleId := range user.RoleIds {
			userRole := domain.SysUserRole{
				UserId: user.UserId,
				RoleId: roleId,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				c.Logger.Error("新增用户角色关联失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "新增失败")
				return
			}
		}
	}

	// 保存用户岗位关联
	if len(user.PostIds) > 0 {
		for _, postId := range user.PostIds {
			userPost := domain.SysUserPost{
				UserId: user.UserId,
				PostId: postId,
			}
			if err := tx.Create(&userPost).Error; err != nil {
				tx.Rollback()
				c.Logger.Error("新增用户岗位关联失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "新增失败")
				return
			}
		}
	}

	tx.Commit()
	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改用户 - 完全按照Java后端业务逻辑
func (c *SysUserController) Edit(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 参数验证
	if user.UserId == 0 {
		c.ErrorWithMessage(ctx, "用户ID不能为空")
		return
	}
	if user.UserName == "" {
		c.ErrorWithMessage(ctx, "用户账号不能为空")
		return
	}

	// 检查是否允许操作用户
	if !c.checkUserAllowed(&user) {
		c.ErrorWithMessage(ctx, "不允许操作超级管理员用户")
		return
	}

	// 数据权限检查
	// TODO: 实现checkUserDataScope、checkDeptDataScope和checkRoleDataScope逻辑

	// 检查用户是否存在
	var existingUser domain.SysUser
	if err := c.db.Where("user_id = ? AND del_flag = ?", user.UserId, "0").First(&existingUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "用户不存在")
		} else {
			c.Logger.Error("查询用户失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 检查用户名唯一性（排除自己）
	if !c.checkUserNameUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 检查手机号唯一性（排除自己）
	if user.Phonenumber != "" && !c.checkPhoneUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 检查邮箱唯一性（排除自己）
	if user.Email != "" && !c.checkEmailUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 开启事务
	tx := c.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新用户基本信息（不包括密码）
	updateData := map[string]interface{}{
		"user_name":   user.UserName,
		"nick_name":   user.NickName,
		"email":       user.Email,
		"phonenumber": user.Phonenumber,
		"sex":         user.Sex,
		"avatar":      user.Avatar,
		"dept_id":     user.DeptId,
		"status":      user.Status,
		"update_by":   user.UpdateBy,
		"remark":      user.Remark,
	}

	if err := tx.Model(&existingUser).Updates(updateData).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("修改用户失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	// 删除原有的用户角色关联
	if err := tx.Where("user_id = ?", user.UserId).Delete(&domain.SysUserRole{}).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户角色关联失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	// 保存新的用户角色关联
	if len(user.RoleIds) > 0 {
		for _, roleId := range user.RoleIds {
			userRole := domain.SysUserRole{
				UserId: user.UserId,
				RoleId: roleId,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				c.Logger.Error("新增用户角色关联失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "修改失败")
				return
			}
		}
	}

	// 删除原有的用户岗位关联
	if err := tx.Where("user_id = ?", user.UserId).Delete(&domain.SysUserPost{}).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户岗位关联失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	// 保存新的用户岗位关联
	if len(user.PostIds) > 0 {
		for _, postId := range user.PostIds {
			userPost := domain.SysUserPost{
				UserId: user.UserId,
				PostId: postId,
			}
			if err := tx.Create(&userPost).Error; err != nil {
				tx.Rollback()
				c.Logger.Error("新增用户岗位关联失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "修改失败")
				return
			}
		}
	}

	tx.Commit()
	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除用户 - 完全按照Java后端业务逻辑
func (c *SysUserController) Remove(ctx *gin.Context) {
	userIdsStr := ctx.Param("userIds")
	userIdStrs := strings.Split(userIdsStr, ",")

	var userIds []int64
	for _, userIdStr := range userIdStrs {
		userId, err := strconv.ParseInt(userIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "用户ID格式错误")
			return
		}
		userIds = append(userIds, userId)
	}

	// 检查是否包含当前用户
	currentUserId := c.GetCurrentUserId(ctx)
	for _, userId := range userIds {
		if userId == currentUserId {
			c.ErrorWithMessage(ctx, "当前用户不能删除")
			return
		}
	}

	// 检查是否包含超级管理员
	for _, userId := range userIds {
		if userId == 1 {
			c.ErrorWithMessage(ctx, "不能删除超级管理员用户")
			return
		}
	}

	// 开启事务
	tx := c.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 软删除用户
	if err := tx.Model(&domain.SysUser{}).Where("user_id IN ?", userIds).Update("del_flag", "2").Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	// 删除用户角色关联
	if err := tx.Where("user_id IN ?", userIds).Delete(&domain.SysUserRole{}).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户角色关联失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	// 删除用户岗位关联
	if err := tx.Where("user_id IN ?", userIds).Delete(&domain.SysUserPost{}).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户岗位关联失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	tx.Commit()
	c.SuccessWithMessage(ctx, "删除成功")
}

// ResetPwd 重置密码 - 完全按照Java后端业务逻辑
func (c *SysUserController) ResetPwd(ctx *gin.Context) {
	var req struct {
		UserId   int64  `json:"userId"`
		Password string `json:"password"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	if req.UserId == 0 || req.Password == "" {
		c.ErrorWithMessage(ctx, "参数不能为空")
		return
	}

	// 检查是否允许操作用户
	user := domain.SysUser{UserId: req.UserId}
	if !c.checkUserAllowed(&user) {
		c.ErrorWithMessage(ctx, "不允许操作超级管理员用户")
		return
	}

	// 密码加密
	encryptedPassword := c.encryptPassword(req.Password)

	// 更新密码
	if err := c.db.Model(&domain.SysUser{}).Where("user_id = ? AND del_flag = ?", req.UserId, "0").Update("password", encryptedPassword).Error; err != nil {
		c.Logger.Error("重置密码失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "重置密码失败")
		return
	}

	c.SuccessWithMessage(ctx, "重置密码成功")
}

// ChangeStatus 状态修改 - 完全按照Java后端业务逻辑
func (c *SysUserController) ChangeStatus(ctx *gin.Context) {
	var req struct {
		UserId int64  `json:"userId"`
		Status string `json:"status"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	if req.UserId == 0 {
		c.ErrorWithMessage(ctx, "用户ID不能为空")
		return
	}

	// 检查是否允许操作用户
	user := domain.SysUser{UserId: req.UserId}
	if !c.checkUserAllowed(&user) {
		c.ErrorWithMessage(ctx, "不允许操作超级管理员用户")
		return
	}

	// 更新状态
	if err := c.db.Model(&domain.SysUser{}).Where("user_id = ? AND del_flag = ?", req.UserId, "0").Update("status", req.Status).Error; err != nil {
		c.Logger.Error("修改用户状态失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改状态失败")
		return
	}

	statusText := "启用"
	if req.Status == "1" {
		statusText = "停用"
	}

	c.SuccessWithMessage(ctx, statusText+"成功")
}

// AuthRole 获取授权角色 - 完全按照Java后端业务逻辑
func (c *SysUserController) AuthRole(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "用户ID格式错误")
		return
	}

	// 查询用户信息
	var user domain.SysUser
	if err := c.db.Where("user_id = ? AND del_flag = ?", userId, "0").First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.ErrorWithMessage(ctx, "用户不存在")
		} else {
			c.Logger.Error("查询用户信息失败", zap.Error(err))
			c.ErrorWithMessage(ctx, "查询失败")
		}
		return
	}

	// 查询所有角色
	var roles []domain.SysRole
	c.db.Model(&domain.SysRole{}).Where("del_flag = ? AND status = ?", "0", "0").Find(&roles)

	// 查询用户已分配的角色
	var userRoles []domain.SysUserRole
	c.db.Where("user_id = ?", userId).Find(&userRoles)

	var roleIds []int64
	for _, ur := range userRoles {
		roleIds = append(roleIds, ur.RoleId)
	}

	c.SuccessWithData(ctx, gin.H{
		"user":    user,
		"roles":   roles,
		"roleIds": roleIds,
	})
}

// InsertAuthRole 用户授权角色 - 完全按照Java后端业务逻辑
func (c *SysUserController) InsertAuthRole(ctx *gin.Context) {
	var req struct {
		UserId  int64   `json:"userId"`
		RoleIds []int64 `json:"roleIds"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	if req.UserId == 0 {
		c.ErrorWithMessage(ctx, "用户ID不能为空")
		return
	}

	// 检查是否允许操作用户
	user := domain.SysUser{UserId: req.UserId}
	if !c.checkUserAllowed(&user) {
		c.ErrorWithMessage(ctx, "不允许操作超级管理员用户")
		return
	}

	// 开启事务
	tx := c.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除原有的用户角色关联
	if err := tx.Where("user_id = ?", req.UserId).Delete(&domain.SysUserRole{}).Error; err != nil {
		tx.Rollback()
		c.Logger.Error("删除用户角色关联失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "授权失败")
		return
	}

	// 保存新的用户角色关联
	if len(req.RoleIds) > 0 {
		for _, roleId := range req.RoleIds {
			userRole := domain.SysUserRole{
				UserId: req.UserId,
				RoleId: roleId,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				c.Logger.Error("新增用户角色关联失败", zap.Error(err))
				c.ErrorWithMessage(ctx, "授权失败")
				return
			}
		}
	}

	tx.Commit()
	c.SuccessWithMessage(ctx, "授权成功")
}

// DeptTree 部门树列表 - 完全按照Java后端业务逻辑
func (c *SysUserController) DeptTree(ctx *gin.Context) {
	var depts []domain.SysDept

	// 查询所有部门
	query := c.db.Model(&domain.SysDept{}).Where("del_flag = ? AND status = ?", "0", "0")
	query = query.Order("parent_id ASC, order_num ASC")

	if err := query.Find(&depts).Error; err != nil {
		c.Logger.Error("查询部门列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	// 构建部门树选择结构
	deptTree := c.buildDeptTreeSelect(depts, 0)

	c.SuccessWithData(ctx, deptTree)
}

// Export 导出用户 - 简化实现
func (c *SysUserController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能暂未实现")
}

// ImportData 导入用户 - 简化实现
func (c *SysUserController) ImportData(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导入功能暂未实现")
}

// ImportTemplate 导入模板 - 简化实现
func (c *SysUserController) ImportTemplate(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导入模板功能暂未实现")
}

// 辅助函数

// checkUserNameUnique 校验用户名是否唯一
func (c *SysUserController) checkUserNameUnique(user *domain.SysUser) bool {
	var count int64
	query := c.db.Model(&domain.SysUser{}).Where("user_name = ? AND del_flag = ?", user.UserName, "0")

	// 如果是修改操作，排除自己
	if user.UserId > 0 {
		query = query.Where("user_id != ?", user.UserId)
	}

	query.Count(&count)
	return count == 0
}

// checkPhoneUnique 校验手机号是否唯一
func (c *SysUserController) checkPhoneUnique(user *domain.SysUser) bool {
	var count int64
	query := c.db.Model(&domain.SysUser{}).Where("phonenumber = ? AND del_flag = ?", user.Phonenumber, "0")

	// 如果是修改操作，排除自己
	if user.UserId > 0 {
		query = query.Where("user_id != ?", user.UserId)
	}

	query.Count(&count)
	return count == 0
}

// checkEmailUnique 校验邮箱是否唯一
func (c *SysUserController) checkEmailUnique(user *domain.SysUser) bool {
	var count int64
	query := c.db.Model(&domain.SysUser{}).Where("email = ? AND del_flag = ?", user.Email, "0")

	// 如果是修改操作，排除自己
	if user.UserId > 0 {
		query = query.Where("user_id != ?", user.UserId)
	}

	query.Count(&count)
	return count == 0
}

// checkUserAllowed 检查是否允许操作用户
func (c *SysUserController) checkUserAllowed(user *domain.SysUser) bool {
	// 不允许操作超级管理员用户
	if user.UserId == 1 {
		return false
	}
	return true
}

// encryptPassword 密码加密
func (c *SysUserController) encryptPassword(password string) string {
	// 使用MD5加密（与Java后端保持一致）
	hash := md5.Sum([]byte(password))
	return fmt.Sprintf("%x", hash)
}

// buildDeptTreeSelect 构建部门树选择结构
func (c *SysUserController) buildDeptTreeSelect(depts []domain.SysDept, parentId int64) []gin.H {
	var tree []gin.H

	for _, dept := range depts {
		if dept.ParentId == parentId {
			children := c.buildDeptTreeSelect(depts, dept.DeptId)

			node := gin.H{
				"id":    dept.DeptId,
				"label": dept.DeptName,
			}

			if len(children) > 0 {
				node["children"] = children
			}

			tree = append(tree, node)
		}
	}

	return tree
}

// 响应辅助函数

// SuccessWithData 成功响应带数据
func (c *SysUserController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": data,
	})
}

// SuccessWithMessage 成功响应带消息
func (c *SysUserController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  message,
	})
}

// ErrorWithMessage 错误响应
func (c *SysUserController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  message,
	})
}

// GetUsername 获取当前用户名
func (c *SysUserController) GetUsername(ctx *gin.Context) string {
	// TODO: 从JWT token中获取用户名
	if username, exists := ctx.Get("username"); exists {
		return username.(string)
	}
	return "admin" // 临时默认值
}

// GetCurrentUserId 获取当前用户ID
func (c *SysUserController) GetCurrentUserId(ctx *gin.Context) int64 {
	// TODO: 从JWT token中获取用户ID
	if userId, exists := ctx.Get("userId"); exists {
		if id, ok := userId.(int64); ok {
			return id
		}
	}
	return 1 // 临时默认值（admin用户）
}
