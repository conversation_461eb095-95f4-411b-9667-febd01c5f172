# WOSM Frontend Compatibility Test - Simple Version

Write-Host "=== WOSM Frontend Compatibility Test ===" -ForegroundColor Blue
Write-Host ""

# 获取认证令牌
Write-Host "1. Authentication Flow..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    $token = $loginResponse.data.token
    $headers = @{ Authorization = "Bearer $token" }
    Write-Host "✅ Login API Compatible" -ForegroundColor Green
} catch {
    Write-Host "❌ Login API Incompatible" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试前端需要的核心API
Write-Host "2. Core Frontend APIs..." -ForegroundColor Yellow

$passCount = 0
$totalCount = 9

# User Info API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Get User Info - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Get User Info - Error" -ForegroundColor Red
}

# Get Routers API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/getRouters" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Get Routers - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Get Routers - Error" -ForegroundColor Red
}

# Captcha API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/captchaImage" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Captcha Image - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Captcha Image - Error" -ForegroundColor Red
}

# User List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ User List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ User List - Error" -ForegroundColor Red
}

# Role List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/role/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Role List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Role List - Error" -ForegroundColor Red
}

# Menu List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/menu/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Menu List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Menu List - Error" -ForegroundColor Red
}

# Dept List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dept/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Dept List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Dept List - Error" -ForegroundColor Red
}

# Post List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/post/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Post List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Post List - Error" -ForegroundColor Red
}

# Dict Type List API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/system/dict/type/list" -Headers $headers -TimeoutSec 10
    if ($response.code -eq 200) {
        Write-Host "✅ Dict Type List - Compatible" -ForegroundColor Green
        $passCount++
    }
} catch {
    Write-Host "❌ Dict Type List - Error" -ForegroundColor Red
}

Write-Host ""

# 测试CRUD操作
Write-Host "3. CRUD Operations..." -ForegroundColor Yellow

# 测试用户CRUD
try {
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $newUserJson = '{"loginName":"test_' + $timestamp + '","userName":"测试用户","email":"<EMAIL>","phonenumber":"13800138000","sex":"0","status":"0","deptId":103,"password":"123456"}'
    $createResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $newUserJson -Headers $headers -ContentType "application/json" -TimeoutSec 10
    
    if ($createResponse.code -eq 200) {
        Write-Host "✅ User Create - Compatible" -ForegroundColor Green
        $testUserId = $createResponse.data.userId
        
        # 测试更新
        $updateUserJson = '{"userId":' + $testUserId + ',"loginName":"upd_test","userName":"更新用户","email":"<EMAIL>","phonenumber":"13900139000","sex":"1","status":"0","deptId":103}'
        $updateResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Put -Body $updateUserJson -Headers $headers -ContentType "application/json" -TimeoutSec 10
        
        if ($updateResponse.code -eq 200) {
            Write-Host "✅ User Update - Compatible" -ForegroundColor Green
        } else {
            Write-Host "❌ User Update - Incompatible" -ForegroundColor Red
        }
        
        # 测试删除
        $deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/$testUserId" -Method Delete -Headers $headers -TimeoutSec 10
        
        if ($deleteResponse.code -eq 200) {
            Write-Host "✅ User Delete - Compatible" -ForegroundColor Green
        } else {
            Write-Host "❌ User Delete - Incompatible" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ User Create - Incompatible" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ CRUD Operations Failed" -ForegroundColor Red
}

Write-Host ""

# 总结
Write-Host "=== Compatibility Summary ===" -ForegroundColor Blue
Write-Host "Core API Compatibility: $passCount/$totalCount" -ForegroundColor White

if ($passCount -eq $totalCount) {
    Write-Host "🎉 EXCELLENT! Go backend is fully compatible with frontend!" -ForegroundColor Green
    Write-Host "Frontend Integration Status: READY FOR PRODUCTION" -ForegroundColor Green
} elseif ($passCount -ge 7) {
    Write-Host "✅ GOOD! Go backend is mostly compatible with frontend" -ForegroundColor Yellow
    Write-Host "Frontend Integration Status: READY WITH MINOR ISSUES" -ForegroundColor Yellow
} else {
    Write-Host "❌ POOR! Go backend needs more work for frontend compatibility" -ForegroundColor Red
    Write-Host "Frontend Integration Status: NEEDS MORE WORK" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Blue
