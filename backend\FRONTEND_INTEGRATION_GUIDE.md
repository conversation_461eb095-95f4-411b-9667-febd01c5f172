# WOSM 前端集成指南

## 🎯 集成概述

WOSM Go后端已经完全兼容现有前端，无需修改前端代码即可正常使用。

## 🔗 API接口地址

### 基础服务地址
```
http://localhost:8080
```

### 前端直接访问接口 (无需/api前缀)
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户登录 | POST | `/login` | 用户登录认证 |
| 获取验证码 | GET | `/captchaImage` | 获取图形验证码 |
| 获取用户信息 | GET | `/getInfo` | 获取当前用户信息 |
| 获取菜单路由 | GET | `/getRouters` | 获取用户菜单权限 |
| 用户登出 | POST | `/logout` | 用户登出 |
| 健康检查 | GET | `/health` | 系统健康检查 |

### 系统管理接口 (需要/api前缀)
| 模块 | 基础路径 | 说明 |
|------|----------|------|
| 用户管理 | `/api/system/user` | 用户CRUD操作 |
| 角色管理 | `/api/system/role` | 角色权限管理 |
| 部门管理 | `/api/system/dept` | 部门组织管理 |
| 岗位管理 | `/api/system/post` | 岗位信息管理 |
| 字典管理 | `/api/system/dict` | 数据字典管理 |
| 配置管理 | `/api/system/config` | 系统参数配置 |
| 通知管理 | `/api/system/notice` | 系统通知公告 |
| 菜单管理 | `/api/system/menu` | 菜单权限管理 |

### 监控管理接口
| 模块 | 基础路径 | 说明 |
|------|----------|------|
| 操作日志 | `/api/monitor/operlog` | 用户操作日志 |
| 登录日志 | `/api/monitor/logininfor` | 用户登录日志 |
| 在线用户 | `/api/monitor/online` | 在线用户管理 |
| 服务监控 | `/api/monitor/server` | 服务器信息监控 |
| 任务调度 | `/api/monitor/job` | 定时任务管理 |
| 缓存监控 | `/api/monitor/cache` | 缓存状态监控 |

## 🔐 认证流程

### 1. 用户登录
```javascript
// 前端登录请求
const loginData = {
  username: 'admin',
  password: 'admin123',
  code: '', // 验证码（可选）
  uuid: ''  // 验证码UUID（可选）
};

fetch('/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(loginData)
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    // 登录成功，保存token
    localStorage.setItem('token', data.data.token);
    console.log('登录成功:', data.data.user);
  } else {
    console.error('登录失败:', data.msg);
  }
});
```

### 2. 获取用户信息
```javascript
// 获取当前用户信息
fetch('/getInfo', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('用户信息:', data.data.user);
    console.log('用户角色:', data.data.roles);
    console.log('用户权限:', data.data.permissions);
  }
});
```

### 3. 获取菜单路由
```javascript
// 获取用户菜单权限
fetch('/getRouters', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('菜单路由:', data.data);
    // 动态生成前端路由
  }
});
```

## 📝 请求格式

### 请求头设置
```javascript
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`, // 需要认证的接口
  'User-Agent': 'WOSM-Frontend/1.0'
};
```

### 响应格式
所有接口统一返回格式：
```json
{
  "code": 200,           // 状态码：200成功，其他失败
  "msg": "操作成功",      // 消息提示
  "data": {              // 数据内容
    // 具体数据
  }
}
```

### 分页数据格式
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 100,        // 总记录数
    "rows": [            // 数据列表
      // 数据项
    ],
    "pageNum": 1,        // 当前页码
    "pageSize": 10       // 每页大小
  }
}
```

## 🛠️ 常用接口示例

### 用户管理
```javascript
// 获取用户列表
fetch('/api/system/user/list?pageNum=1&pageSize=10', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// 创建用户
fetch('/api/system/user', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    userName: 'testuser',
    nickName: '测试用户',
    email: '<EMAIL>',
    phonenumber: '13800138000',
    sex: '1',
    status: '0'
  })
});

// 更新用户
fetch('/api/system/user', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    userId: 1,
    userName: 'admin',
    nickName: '管理员',
    email: '<EMAIL>'
  })
});

// 删除用户
fetch('/api/system/user/1,2,3', {
  method: 'DELETE',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 角色管理
```javascript
// 获取角色列表
fetch('/api/system/role/list', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// 创建角色
fetch('/api/system/role', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    roleName: '测试角色',
    roleKey: 'test',
    roleSort: 3,
    status: '0',
    menuIds: [1, 2, 3]
  })
});
```

### 部门管理
```javascript
// 获取部门树
fetch('/api/system/dept/list', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// 创建部门
fetch('/api/system/dept', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    parentId: 100,
    deptName: '测试部门',
    orderNum: 1,
    leader: '部门负责人',
    phone: '13800138000',
    email: '<EMAIL>',
    status: '0'
  })
});
```

## 🔍 错误处理

### 常见错误码
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 重新登录 |
| 403 | 权限不足 | 提示权限不足 |
| 404 | 接口不存在 | 检查接口路径 |
| 500 | 服务器错误 | 联系管理员 |

### 错误处理示例
```javascript
fetch('/api/system/user/list', {
  headers: { 'Authorization': `Bearer ${token}` }
})
.then(response => {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
})
.then(data => {
  if (data.code === 200) {
    // 成功处理
    console.log('数据:', data.data);
  } else if (data.code === 401) {
    // 未授权，跳转登录
    window.location.href = '/login';
  } else {
    // 其他错误
    console.error('错误:', data.msg);
  }
})
.catch(error => {
  console.error('网络错误:', error);
});
```

## 🔧 开发调试

### 健康检查
```javascript
// 检查后端服务状态
fetch('/health')
.then(response => response.json())
.then(data => {
  console.log('服务状态:', data.status);
  console.log('服务版本:', data.version);
});
```

### 调试工具
- **浏览器开发者工具**: 查看网络请求
- **Postman**: API接口测试
- **系统日志**: `logs/app.log`

### 常见问题
1. **CORS跨域**: 后端已配置CORS支持
2. **Token过期**: 自动刷新或重新登录
3. **接口404**: 检查路径是否正确
4. **权限403**: 检查用户权限配置

## 📱 移动端适配

### 响应式设计
- 后端接口完全支持移动端
- 返回数据格式统一
- 支持触摸操作

### 移动端特殊处理
```javascript
// 检测移动设备
const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

if (isMobile) {
  // 移动端特殊处理
  headers['X-Device-Type'] = 'mobile';
}
```

## 🚀 性能优化

### 请求优化
1. **缓存Token**: 避免重复登录
2. **请求合并**: 减少网络请求
3. **分页加载**: 大数据量分页处理
4. **懒加载**: 按需加载数据

### 缓存策略
```javascript
// 缓存用户信息
const cacheUserInfo = (userInfo) => {
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
  localStorage.setItem('userInfoTime', Date.now());
};

// 获取缓存的用户信息
const getCachedUserInfo = () => {
  const userInfo = localStorage.getItem('userInfo');
  const cacheTime = localStorage.getItem('userInfoTime');
  
  // 缓存有效期30分钟
  if (userInfo && cacheTime && (Date.now() - cacheTime < 30 * 60 * 1000)) {
    return JSON.parse(userInfo);
  }
  return null;
};
```

---

## 🎉 集成完成

**WOSM Go后端与前端集成已完成！**

- ✅ **接口完全兼容**: 前端无需修改
- ✅ **认证流程正常**: JWT令牌认证
- ✅ **权限控制完整**: RBAC权限模型
- ✅ **数据格式统一**: 标准JSON响应
- ✅ **错误处理完善**: 统一错误码

**现在您可以正常使用前端系统了！** 🚀
